package tw.omos.guests

import android.Manifest
import android.content.Context
import android.os.Bundle
import android.os.Environment
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterShellArgs
import pub.devrel.easypermissions.EasyPermissions
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException


open class HotFixActivity : FlutterActivity(), EasyPermissions.PermissionCallbacks {
    private val READ_AND_WRITE_STORAGE = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE)
    private val HOTFIX_FILENAME = "hotlibapp.so"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //申請許可權
        // EasyPermissions.requestPermissions(this@HotFixActivity, "", 123, *READ_AND_WRITE_STORAGE)
    }

    override fun getFlutterShellArgs(): FlutterShellArgs {
        copyLibAndWrite(this, HOTFIX_FILENAME)
        val supFA = super.getFlutterShellArgs()
        val dir = getDir("libs", Context.MODE_PRIVATE)
        val libPath = dir.absolutePath + File.separator + HOTFIX_FILENAME
        val libFile = File(libPath)
        if (libFile.exists()) {
            supFA.add("--aot-shared-library-name=${libPath}") //如果有hotlibapp檔案 ,設定進去,沒有則作用預設的
            supFA.add("--aot-shared-library-name=${applicationInfo.nativeLibraryDir}/$libPath") //如果有hotlibapp檔案 ,設定進去,沒有則作用預設的
        }
        return supFA
    }

    // 作用: 在手機根目錄找 hotlibapp.so 檔案 , 如果有則複製到 app libs 檔案下, 沒有則不做操作
    fun copyLibAndWrite(context: Context, fileName: String) {
        try {
            val path: String = Environment.getExternalStorageDirectory().toString()
            val destFile2 = File("$path/$fileName")
            if (destFile2.exists()) {
                val dir: File = context.getDir("libs", MODE_PRIVATE)
                val destFile = File(dir.getAbsolutePath() + File.separator.toString() + fileName)
                if (destFile.exists()) {
                    destFile.delete()
                }
                destFile.createNewFile()
                val inputStream = FileInputStream(destFile2)
                val fos = FileOutputStream(destFile)
                val buffer = ByteArray(inputStream.available())
                var byteCount: Int = 0
                while (inputStream.read(buffer).also({ byteCount = it }) != -1) {
                    fos.write(buffer, 0, byteCount)
                }
                fos.flush()
                inputStream.close()
                fos.close()
                destFile2.delete() //複製完後刪除這個檔案
            }
        } catch (e: IOException) {
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
//        TODO("Not yet implemented")
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
//        TODO("Not yet implemented")
    }
}