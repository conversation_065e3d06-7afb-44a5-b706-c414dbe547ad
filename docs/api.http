# 宣告
@baseUrl = {{scheme}}://{{host}}
@token = {{login.response.body.token}}
@tokenr = {{renew.response.body.token}}

################################################################################
### Login 店長、店員登入
################################################################################
# @name login

POST {{baseUrl}}/login  HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json

client_code={{client}}
&channel_code={{channel}}
&username={{username}}
&password={{password}}

################################################################################
### Renew
################################################################################
# @name renew

GET {{baseUrl}}/renew HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{token}}

################################################################################
### logout
################################################################################

GET {{baseUrl}}/logout HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

################################################################################
### 店員、店長帳號列表
################################################################################

GET {{baseUrl}}/store-accounts HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

################################################################################
### 變更密碼
################################################################################

POST {{baseUrl}}/profile/password-reset HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

old_password=123456
&new_password=123456
&check_password=123456

################################################################################
### 訂單列表
################################################################################

GET {{baseUrl}}/orders
?page=1
&limit=50
&status=2 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

################################################################################
### 取得單一消費訂單資料
################################################################################

GET {{baseUrl}}/orders/487 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

################################################################################
# 新增訂單
################################################################################

POST {{baseUrl}}/orders HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

member_id=2
&carrier_type=3
&discount=20
&subtotal=2980
&total=12
&redeem_member_points=30
&random_number=1234
&additional_charges=30
&point_get=100
&point_discount_limit=100
&paid=300
&invoice=true
&member_coupon_id=1
&change=30
&product_price=1000
&coupon_discount=100
&invoice_number=GG12345678
&npo_ban=987
&vat_number=123456789

################################################################################
### 新增訂單 (最少欄位)
################################################################################

POST {{baseUrl}}/orders HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

discount=0
&product_price=1
&subtotal=1
&total=1

################################################################################
### 訂單發票
################################################################################
# @prompt id 訂單 id

POST {{baseUrl}}/orders/{{id}}/invoice HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

invoice_number=KL00000001
&random_number=1234
&invoice_paper=true
# vat_number
# carrier_type
# carrier_id
# npo_ban

################################################################################
### 取得單一品牌資料
################################################################################

GET {{baseUrl}}/brands/info HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

################################################################################
### 更新品牌資料
################################################################################

PUT {{baseUrl}}/brands HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

name=全家就是你家
&line_channel_id=1234567890
&line_secret_code=1wedga5di68jxskf9o2je84hfckls9
&line_name=全家@FamilyIsYourMart

# ccityarea_id=1
# &line_channel_id=1234567890
# &name=全家就是你家
# &post_code=12352
# &phone=(02)2222-5252
# &city_id=1
# &tax_id=12345678
# &business_hours={"週一到週二 12:00 ~ 22:00, 週二公休":null}
# &other={"google-map":{"x":"123111.11","y":"123111.11"},"website":"https://www.omos.tw","shopping_website":"https://shop.omos.tw","fb":"https://www.facebook.com.tw/1234567","ig":"https://www.ig.com.tw/1234567","line":"https://www.line.com.tw/1234567","google":"https://www.google.com.tw/1234567"}
# &line_secret_code=1wedga5di68jxskf9o2je84hfckls9
# &line_name=全家@FamilyIsYourMart
# &point_ratio=1
 
################################################################################
### 取得單一通路資料
################################################################################

GET {{baseUrl}}/channels/info HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}
