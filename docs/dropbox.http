# var

@host = https://api.dropbox.com
@app_key = 9fkzp5d75m72bu5
@app_secret = dw1ao4ui29qe9is
@refresh_token = skOEPJg6Vv0AAAAAAAAAAXcS5lhb-D5IwNmoOhBOqTtogM3-4xTQyCdEt6v46HS6

### get code (使用網頁開啟，用來取得 code)

GET https://www.dropbox.com/oauth2/authorize
?client_id=9fkzp5d75m72bu5
&token_access_type=offline
&response_type=code

### get refresh_token & access_token from code
# @name get_token
# @prompt code 8FkkevIDaa8AAAAAAAAAcHC3MUkmwFirzwKM1vkprKY

curl {{host}}/oauth2/token \
-d code={{code}} \
-d grant_type=authorization_code \
-u {{app_key}}:{{app_secret}}

### get access_toke from refresh_token
# @name get_token

curl {{host}}/oauth2/token \
-d grant_type=refresh_token \
-d refresh_token={{refresh_token}} \
-u {{app_key}}:{{app_secret}}