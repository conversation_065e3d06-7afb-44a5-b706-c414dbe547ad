{"version": 6, "entities": [{"entity": {"type": "Project", "description": "版本格式：主版號.次版號.修訂號，版號遞增規則如下：<br>\n<br>\n主版號：當你做了不相容的 API 修改，<br>\n次版號：當你做了向下相容的功能性新增，<br>\n修訂號：當你做了向下相容的問題修正。<br>\n<br>\n先行版號及版本編譯資訊可以加到「主版號.次版號.修訂號」的後面，作為延伸。<br>\n<br>\nQR-Code 掃描後格式說明：\n  * `會員碼`：{ member_id: 1}\n  * `優惠碼`：{ member_id: 1, member_coupon_id: 1}\n  * `問卷核銷`：{ member_id: 1, member_questionnaire_id: 1}\n\n\nContact Support:\n Email: <EMAIL>", "id": "813a83c6-6b68-4bb9-802b-2bf479483c6a", "name": "OMOS POS APP API"}, "children": [{"entity": {"type": "Service", "id": "306c4146-c2e5-43c1-8620-a481538caba6", "name": "brands"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/brands/info"}, "id": "bc7a91bc-423e-4b76-9381-05e8c23f9b26", "name": "取得單一品牌資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/brands"}, "id": "8de26416-493c-4fcd-bdeb-61ad05399ae3", "name": "更新品牌資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "e0eaef78-fa46-46cd-9ae7-e927fbf0860f", "name": "channels"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/channels/info"}, "id": "a002d067-84c8-416d-9176-fddb429cf7f6", "name": "取得單一通路資料", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/channels"}, "id": "78830dd2-50e0-40a3-aca2-26302c402cee", "name": "更新通路資料", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "62680d06-f9d6-40d9-b4d7-9c95210055d5", "name": "cooperation"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cooperation"}, "id": "0c4e5864-36f6-4378-b65c-cf9df0247a30", "name": "新增跨界邀請", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cooperation"}, "id": "0958cb32-f803-4841-a7e9-08c06468b357", "name": "更新跨界邀請", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"enabled": true, "name": "page", "value": "1"}, {"enabled": true, "name": "limit", "value": "50"}, {"enabled": true, "name": "status", "value": "1"}, {"name": "keyword", "value": "<string>"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cooperation"}, "id": "c9e003b7-8e6a-46bb-bfe1-40a04d84f683", "name": "跨界邀請列表", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "315d69ac-ae2a-4925-8317-e6f8f9e7be2b", "name": "cooperation-coupons"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cooperation-coupons"}, "id": "29f07872-2c1a-4f26-9a91-687cc894e9d6", "name": "新增跨界優惠卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cooperation-coupons"}, "id": "6fbb1086-3a49-433a-b00a-29b2d636a0b1", "name": "更新跨界優惠卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"enabled": true, "name": "page", "value": "1"}, {"enabled": true, "name": "limit", "value": "50"}, {"enabled": true, "name": "status", "value": "0"}, {"name": "keyword", "value": "<string>"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cooperation-coupons"}, "id": "34f65e82-9686-407f-b8f7-213abfa4d996", "name": "跨界優惠卷列表", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "caa615b5-ace1-4d48-a94a-cf2b8b3c6520", "name": "coupons"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"name": "page", "value": "1"}, {"name": "limit", "value": "50"}, {"enabled": true, "name": "status", "value": "1"}, {"name": "keyword", "value": "<string>"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/coupons"}, "id": "70a35213-142b-47f1-ae7a-7dfd2ae8a621", "name": "優惠卷列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.5", "name": "DELETE"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/coupons/${couponId}"}, "id": "493b7192-4f99-4d62-bf3b-416c81fa2684", "name": "刪除單一優惠卷資料", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/coupons/${couponId}"}, "id": "c2686c9c-1dc6-4fb7-a847-b9cf3ae6b4ac", "name": "取得單一優惠卷資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "title", "value": "aaa"}, {"type": "Text", "name": "description", "value": ""}, {"type": "Text", "name": "image_id", "value": ""}, {"enabled": true, "type": "Text", "name": "promotion_type", "value": "0"}, {"enabled": true, "type": "Text", "name": "target", "value": "0"}, {"enabled": true, "type": "Text", "name": "min_price", "value": "1"}, {"type": "Text", "name": "quantity_issued", "value": ""}, {"enabled": true, "type": "Text", "name": "usage_period", "value": "100"}, {"type": "Text", "name": "can_use_points", "value": ""}, {"type": "Text", "name": "is_parallel_other", "value": ""}, {"type": "Text", "name": "can_exchange", "value": ""}, {"enabled": true, "type": "Text", "name": "status", "value": "1"}, {"type": "Text", "name": "exchange_points", "value": ""}, {"type": "Text", "name": "exchange_start_time", "value": ""}, {"type": "Text", "name": "exchange_end_time", "value": ""}, {"type": "Text", "name": "exchange_count_limit", "value": ""}, {"type": "Text", "name": "discount", "value": ""}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/coupons"}, "id": "330dd1aa-9033-494d-99d9-50ab0a3822e0", "name": "新增優惠卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "title", "value": ""}, {"type": "Text", "name": "description", "value": ""}, {"type": "Text", "name": "image_id", "value": ""}, {"enabled": true, "type": "Text", "name": "promotion_type", "value": ""}, {"enabled": true, "type": "Text", "name": "target", "value": "1"}, {"enabled": true, "type": "Text", "name": "min_price", "value": "99"}, {"type": "Text", "name": "quantity_issued", "value": ""}, {"enabled": true, "type": "Text", "name": "usage_period", "value": "100"}, {"type": "Text", "name": "can_use_points", "value": ""}, {"type": "Text", "name": "is_parallel_other", "value": ""}, {"type": "Text", "name": "can_exchange", "value": ""}, {"enabled": true, "type": "Text", "name": "status", "value": "1"}, {"type": "Text", "name": "exchange_points", "value": ""}, {"type": "Text", "name": "exchange_start_time", "value": ""}, {"type": "Text", "name": "exchange_end_time", "value": ""}, {"type": "Text", "name": "exchange_count_limit", "value": ""}, {"type": "Text", "name": "discount", "value": ""}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/coupons/${couponId}"}, "id": "ef0302a4-d334-4c1f-951e-30b8b2c3ff08", "name": "更新優惠卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "07d52471-2544-4a1b-81e8-f83cfbd41475", "name": "images"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/images"}, "id": "1befb0c8-bbfd-426c-a32d-48b330a1a192", "name": "圖片上傳", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"enabled": true, "name": "page", "value": "1"}, {"enabled": true, "name": "limit", "value": "50"}]}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/images"}, "id": "5418e28f-42bb-4049-a257-6feca9208630", "name": "圖片庫列表", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "57770d39-0821-48eb-99f3-afc5c2c52eb4", "name": "member-coupons"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/member-coupons/used"}, "id": "cb3d3fef-4956-4b08-b5e1-87abb22c20f4", "name": "使用會員優惠卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/member-coupons/give"}, "id": "28e682d9-9720-43df-bb22-6922bfd9deea", "name": "發放會員優惠卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "da49dd17-8002-458f-b984-435efe1cfd25", "name": "member-points"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "points", "value": "2"}, {"type": "Text", "name": "expiry_date", "value": "2021-04-19T06:10:11"}, {"enabled": true, "type": "Text", "name": "type", "value": "1"}, {"enabled": true, "type": "Text", "name": "comment", "value": "aaa"}, {"type": "Text", "name": "created_at", "value": ""}, {"type": "Text", "name": "updated_at", "value": ""}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/member-points/${memberId}"}, "id": "ab4777c6-94ee-49ac-958a-6b167f868f41", "name": "新增/減少會員積點 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "1027eb9b-32a5-4478-862b-ae15d79fa3e8", "name": "member-promotions"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/member-promotions"}, "id": "e18dac16-abc6-4b20-a274-de1b6e40c0fe", "name": "更新會員活動", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/member-promotions"}, "id": "fffaef48-6460-4b69-86d6-05635bb08718", "name": "會員活動資料：入會禮＋生日禮＋會員推薦", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "b86cd023-134d-4195-84ff-b4996b71dd77", "name": "members"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members/2"}, "id": "2a1ef152-a170-4db0-8f90-af5aa2a48e6b", "name": "取得單一會員資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members/${memberId}/orders"}, "id": "4a2c6c29-c89d-46fa-bf28-c8e4a58c6a57", "name": "品牌會員訂單列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"name": "page", "value": "1"}, {"name": "limit", "value": "50"}, {"name": "keyword", "value": "aaa"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members"}, "id": "8161484e-2e89-4046-b21a-9771520eff55", "name": "品牌會員資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "b1e1c6ba-c4a8-4139-be9c-53da37695178", "name": "orders"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/orders/${orderId}"}, "id": "599261e5-b55a-48cd-b977-7083fac44483", "name": "取得單一消費訂單資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"type": "Text", "name": "member_id", "value": "2"}, {"enabled": true, "type": "Text", "name": "product_price", "value": "1000"}, {"enabled": true, "type": "Text", "name": "subtotal", "value": "2980"}, {"enabled": true, "type": "Text", "name": "total", "value": "12"}, {"type": "Text", "name": "member_coupon_id", "value": "1"}, {"type": "Text", "name": "coupon_discount", "value": "-100"}, {"type": "Text", "name": "redeem_member_points", "value": "30"}, {"type": "Text", "name": "point_discount_limit", "value": "100"}, {"type": "Text", "name": "point_get", "value": "100"}, {"type": "Text", "name": "created_at", "value": "2020-01-01 13:20:29"}, {"type": "Text", "name": "updated_at", "value": "2020-01-07 14:30:21"}]}, "bodyType": "Form", "textBody": "{\n  \"product_price\": 1000,\n  \"subtotail\": 2980,\n  \"total\": 12\n}"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/orders"}, "id": "098c0c5d-8163-46e7-8118-6cddaeb37b9f", "name": "新增訂單 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"name": "page", "value": "1"}, {"enabled": true, "name": "limit", "value": "50"}, {"name": "client_id", "value": "<integer>"}, {"name": "brand_id", "value": "<integer>"}, {"name": "channel_id", "value": "<integer>"}, {"name": "status", "value": "2"}, {"name": "keyword", "value": "gmail"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/orders"}, "id": "42dec837-b7d5-41bd-8cdd-f81a74f7cea2", "name": "訂單列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/orders/refund"}, "id": "d0dd9130-337e-452c-a39f-08e9021192e1", "name": "訂單退款 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "98fceb0f-6dbf-4d76-8f64-9af947d4cb74", "name": "promotions/coupon"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"name": "page", "value": "1"}, {"name": "limit", "value": "50"}, {"name": "status", "value": "0"}, {"name": "keyword", "value": "<varchar>"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/promotions/coupon"}, "id": "b7b9d30b-ed70-4646-9085-902ee09ce2ce", "name": "優惠活動列表", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/promotions/coupon"}, "id": "b99b6fa1-6329-4faf-80a9-f73ce43eb742", "name": "新增優惠活動", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/promotions/coupon"}, "id": "cbe75244-9572-415a-9830-d6fda3b61c8f", "name": "更新優惠活動", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "7883c727-2094-4911-96c5-93a70c9040df", "name": "questionnaires"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "DELETE"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/questionnaires/2"}, "id": "54fa05ed-f223-488e-b3f1-71cb4a1ec1a9", "name": "刪除單一問卷資料", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/questionnaires/2"}, "id": "3fb78a02-a27b-417d-9929-d8e7e2bff71a", "name": "取得單一問卷資料", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"name": "page", "value": "1"}, {"name": "limit", "value": "50"}, {"name": "status", "value": "0"}, {"name": "keyword", "value": ""}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/questionnaires"}, "id": "36b46e5c-7aec-4fea-be70-c262c1b77c46", "name": "問卷列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/questionnaires"}, "id": "f6d6f9aa-5d2f-45d5-9ed4-a0f353e0445d", "name": "新增問卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/questionnaires"}, "id": "cd98f3d9-25ab-487d-900d-61cc914bbd94", "name": "更新問卷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "dad6cde8-e395-4a6e-aa87-df16ddc62840", "name": "store-accounts"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/store-accounts/${storeAccount}"}, "id": "87db3cf6-cf2d-4a61-ace6-e02116cd1eb8", "name": "取得單一帳號資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/store-accounts"}, "id": "595e9f68-6723-4e92-a0ae-9563a00757b4", "name": "店員、店長帳號列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "role_id", "value": "2"}, {"enabled": true, "type": "Text", "name": "username", "value": "aaa"}, {"enabled": true, "type": "Text", "name": "name", "value": "bbb"}, {"enabled": true, "type": "Text", "name": "password", "value": "aaa"}, {"enabled": true, "type": "Text", "name": "status", "value": "1"}, {"type": "Text", "name": "comment", "value": ""}, {"type": "Text", "name": "created_at", "value": ""}, {"type": "Text", "name": "updated_at", "value": ""}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/store-accounts"}, "id": "e73ff97e-5245-45b1-af01-d1e978db3801", "name": "新增帳號 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "role_id", "value": "1"}, {"enabled": true, "type": "Text", "name": "name", "value": "林姓員工1"}, {"type": "Text", "name": "password", "value": ""}, {"enabled": true, "type": "Text", "name": "status", "value": "1"}, {"type": "Text", "name": "comment", "value": ""}, {"type": "Text", "name": "updated_at", "value": ""}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/store-accounts/1"}, "id": "781bbee1-6764-4631-ab9d-7da6885103f9", "name": "更新帳號 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}]}, {"entity": {"type": "Service", "id": "d7cbb493-217c-4dd0-90ae-60a2c6bbc098", "name": "{coupon id}"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "DELETE"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/coupons/:coupon_id"}, "id": "b087a086-8859-4777-8dcf-2a056e8b34dc", "name": "刪除單一優惠卷資料", "headers": [], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/coupons/:coupon_id"}, "id": "c937ffa9-42cf-4b5f-9bf2-69496e5a2231", "name": "取得單一優惠卷資料", "headers": [], "assertions": []}}]}, {"entity": {"type": "Service", "id": "2907aa3f-9e1e-4061-bd37-5ecd63fb9d6f", "name": "{image id}"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "DELETE"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/images/:image_id"}, "id": "3bd79820-63bc-41d5-abfd-9b0eddfe2b78", "name": "刪除單一圖片資料", "headers": [], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"enabled": true, "name": "image_id", "value": "2"}]}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/images"}, "id": "afbd1abf-a222-4d69-a8bb-e7bf43ee38a3", "name": "取得單一圖片資料", "headers": [], "assertions": []}}]}, {"entity": {"type": "Service", "id": "be4060c6-ac33-424f-88b8-d3d806ca1eec", "name": "{member id}"}, "children": [{"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": [{"enabled": true, "name": "page", "value": "1"}, {"enabled": true, "name": "limit", "value": "50"}]}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/member-points/:member_id"}, "id": "75c604ca-d9d0-4f1c-8ca0-7c4f9f3b165a", "name": "會員積點列表", "headers": [], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/member-points/:member_id/total"}, "id": "60f54452-5bd7-46f0-a16a-d41c77b15ce0", "name": "會員總積點", "headers": [], "assertions": []}}]}, {"entity": {"type": "Service", "id": "59a2501e-561f-4d38-84ce-b417ab01ef7d", "name": "{promotion id}"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "DELETE"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/promotions/coupon/:promotion_id"}, "id": "4ee44908-1865-4b1b-9be4-8ae6575f85b6", "name": "刪除單一優惠活動資料", "headers": [], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/promotions/coupon/:promotion_id"}, "id": "3ef9980f-2480-4e22-936e-c13c0b44f257", "name": "取得單一優惠活動資料", "headers": [], "assertions": []}}]}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/renew"}, "id": "8ccca082-6ef1-4303-9ae2-437c4099f297", "name": "token 更新", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"scheme": {"name": "http", "version": "V11"}, "path": "${baseUrl}/clients/info"}, "id": "dac7a2f6-eae2-4f91-8852-3c43e46e1bd7", "name": "取得單一集團資料", "headers": [], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cities"}, "id": "53e85c76-873e-4013-8614-1d95fc48b7ca", "name": "城市列表", "headers": [], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "client_code", "value": "omos"}, {"enabled": true, "type": "Text", "name": "channel_code", "value": "zhōngxiào"}, {"enabled": true, "type": "Text", "name": "username", "value": "um_mg"}, {"enabled": true, "type": "Text", "name": "password", "value": "123456"}, {"type": "Text", "name": "username", "value": "um"}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/login"}, "description": "使用帳號密碼登入（JWT 格式）", "id": "7e0809ed-4208-4158-89d1-15b7ba7e1150", "name": "店長、店員登入", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/logout"}, "description": "登出系統", "id": "a80ce085-1c51-40db-89fb-b158d721b345", "name": "店長、店員登出", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "PUT"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/member-questionnaires/used"}, "id": "319f809a-eac7-492e-a568-6a8b2ea11519", "name": "會員問卷核銷", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "old_password", "value": "123456"}, {"enabled": true, "type": "Text", "name": "new_password", "value": "123456"}, {"enabled": true, "type": "Text", "name": "check_password", "value": "123456"}]}, "bodyType": "Form", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/profile/password-reset"}, "description": "重新設定登入者密碼", "id": "26b83d61-6a2d-4e4e-b02c-37ef54b13851", "name": "變更密碼 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "POST"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/carts/promotion"}, "description": "購物車中符合活動的活動資訊", "id": "d30bfe64-3a01-4b26-beae-05998b5a353f", "name": "購物車活動清單", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"link": "https://tools.ietf.org/html/rfc7231#section-4.3", "name": "GET"}, "body": {"bodyType": "Text", "textBody": ""}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/cityares"}, "id": "1bdafa7a-a72b-4df2-bd15-a4ba9f558c06", "name": "鄉鎮列表", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}], "assertions": []}}]}, {"entity": {"type": "Project", "id": "3c01e43e-a4ff-48f1-8d58-f02baa07a4ee", "name": "金財通"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text", "textBody": "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n    <soap:Body>\n        <GetInvoiceRange xmlns=\"http://tempuri.org/\">\n            <machineNo>POS</machineNo>\n            <APIKey>${apikey}</APIKey>\n            <deptCode></deptCode>\n            <invoiceYear>2021</invoiceYear>\n            <MainBAN>${posBAN}</MainBAN>\n            <invoiceMonth>3</invoiceMonth>\n            <sellerBAN>${posBAN}</sellerBAN>\n            <invoiceType>35</invoiceType>\n        </GetInvoiceRange>\n    </soap:Body>\n</soap:Envelope>"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${host}", "path": "/WebService_Npoi2Test/PosInvoiceRange.asmx"}, "id": "27006202-cf3e-46c1-98a7-8e5959f78311", "name": "0. 取得發票小區間配號", "headers": [{"enabled": true, "name": "Content-Type", "value": "text/xml"}, {"name": "Content-Length", "value": "620"}, {"name": "SOAPAction", "value": "http://tempuri.org/GetInvoiceRange"}, {"name": "Host", "value": "${host}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "PosBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "${apikey}"}, {"enabled": true, "type": "Text", "name": "SellerBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "StoreCode", "value": "OK001"}, {"enabled": true, "type": "Text", "name": "StoreName", "value": "OKSHOP"}, {"enabled": true, "type": "Text", "name": "RegisterCode", "value": "M1"}, {"enabled": true, "type": "Text", "name": "OrderNo", "value": "OKSHOP201910006"}, {"enabled": true, "type": "Text", "name": "State", "value": "1"}, {"enabled": true, "type": "Text", "name": "InvoiceNo", "value": "TZ00000002"}, {"enabled": true, "type": "Text", "name": "InvoiceDate", "value": "2021/04/06 10:10:10"}, {"enabled": true, "type": "Text", "name": "AllowanceDate", "value": ""}, {"enabled": true, "type": "Text", "name": "BuyerBAN", "value": ""}, {"enabled": true, "type": "Text", "name": "PringMark", "value": ""}, {"enabled": true, "type": "Text", "name": "MemberId", "value": ""}, {"enabled": true, "type": "Text", "name": "CheckNo", "value": ""}, {"enabled": true, "type": "Text", "name": "InvoiceType", "value": "32"}, {"enabled": true, "type": "Text", "name": "GroupMark", "value": ""}, {"enabled": true, "type": "Text", "name": "SalesAmt", "value": "100"}, {"enabled": true, "type": "Text", "name": "FreeTaxSalesAmt", "value": "0"}, {"enabled": true, "type": "Text", "name": "ZeroTaxSalesAmt", "value": "0"}, {"enabled": true, "type": "Text", "name": "TaxAmt", "value": "0"}, {"enabled": true, "type": "Text", "name": "TotalAmt", "value": "100"}, {"enabled": true, "type": "Text", "name": "TaxType", "value": "1"}, {"enabled": true, "type": "Text", "name": "TaxRate", "value": "0.05"}, {"enabled": true, "type": "Text", "name": "DiscountAmt", "value": "0"}, {"enabled": true, "type": "Text", "name": "HealthyAmt", "value": "0"}, {"enabled": true, "type": "Text", "name": "CarrierType", "value": ""}, {"enabled": true, "type": "Text", "name": "CarrierId1", "value": ""}, {"enabled": true, "type": "Text", "name": "CarrierId2", "value": ""}, {"enabled": true, "type": "Text", "name": "NpoBan", "value": ""}, {"enabled": true, "type": "Text", "name": "RandomNumber", "value": "3097"}, {"enabled": true, "type": "Text", "name": "MainRemark", "value": ""}, {"enabled": true, "type": "Text", "name": "InvoiceDetails", "value": ""}, {"enabled": true, "type": "Text", "name": "FormatType", "value": ""}]}, "bodyType": "Text", "textBody": "[\n  {\n    \"PosBAN\": \"83193989\",\n    \"ApiKey\": \"S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY\",\n    \"State\": 4,\n    \"SellerBAN\": \"83193989\",\n    \"StoreCode\": \"OK001\",\n    \"StoreName\": \"OKSHOP\",\n    \"RegisterCode\": \"aaabbb\",\n    \"OrderNo\": \"OKSHOP201910006\",\n    \"InvoiceNo\": \"TZ00000011\",\n    \"InvoiceDate\": \"2021/04/06 10:10:10\",\n    \"AllowanceDate\": \"2021/04/06 10:10:10\",\n    \"BuyerBAN\": \"\",\n    \"PrintMark\": \"Y\",\n    \"MemberId\": \"\",\n    \"CheckNo\": \"\",\n    \"InvoiceType\": \"32\",\n    \"GroupMark\": \"\",\n    \"SalesAmt\": 100,\n    \"FreeTaxSalesAmt\": 0,\n    \"ZeroTaxSalesAmt\": 0,\n    \"TaxAmt\": 0,\n    \"TotalAmt\": 100,\n    \"TaxType\": \"1\",\n    \"TaxRate\": 0.05,\n    \"DiscountAmt\": 0,\n    \"HealthyAmt\": 0,\n    \"CarrierType\": \"\",\n    \"CarrierId1\": \"\",\n    \"CarrierId2\": \"\",\n    \"NpoBan\": \"\",\n    \"RandomNumber\": \"3097\",\n    \"MainRemark\": \"\",\n    \"FormatType\": \"\",\n    \"InvoiceDetails\": [\n      {\n        \"SequenceNo\": \"1\",\n        \"ItemName\": \"巧克力蛋糕\",\n        \"Qty\": 1,\n        \"Unit\": \"件\",\n        \"UnitPrice\": 100,\n        \"SalesAmt\": 100,\n        \"TaxAmt\": 0,\n        \"TotalAmt\": 100,\n        \"DiscountAmt\": 0,\n        \"HealthAmt\": 0,\n        \"RelateNumber\": \"\",\n        \"Remark\": \"\"\n      }]\n  }]"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/PosInvoiceNews"}, "id": "81a0a8d3-fb5e-43f5-b39f-2faac4606e1f", "name": "1. 開立發票/折讓", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/json"}], "assertions": []}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "PosBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "${apiKey}"}, {"enabled": true, "type": "Text", "name": "SellerBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "StoreCode", "value": "OK001"}, {"enabled": true, "type": "Text", "name": "StoreName", "value": "OKSHOP"}, {"enabled": true, "type": "Text", "name": "RegisterCode", "value": "M1"}, {"enabled": true, "type": "Text", "name": "OrderNo", "value": "OKSHOP201910005"}, {"enabled": true, "type": "Text", "name": "State", "value": "2"}, {"enabled": true, "type": "Text", "name": "InvoiceNo", "value": "TZ00000001"}, {"enabled": true, "type": "Text", "name": "InvoiceDate", "value": "2019/10/09 16:47:37"}, {"enabled": true, "type": "Text", "name": "CancelDate", "value": "2019/10/09 16:47:37"}, {"enabled": true, "type": "Text", "name": "BuyerBAN", "value": ""}, {"enabled": true, "type": "Text", "name": "AllowanceDate", "value": "2013/03/19"}]}, "bodyType": "Text", "textBody": "[\n    {\n        \"PosBAN\": \"83193989\",\n        \"ApiKey\": \"S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY\",\n        \"State\": 5,\n        \"SellerBAN\": \"83193989\",\n        \"StoreCode\": \"OK001\",\n        \"StoreName\": \"OKSHOP\",\n        \"RegisterCode\": \"M1\",\n        \"OrderNo\": \"OKSHOP201910004\",\n        \"InvoiceNo\": \"TZ00000011\",\n        \"InvoiceDate\": \"2021/04/06 16:47:37\",\n        \"AllowanceDate\": \"2021/04/06 16:47:37\",\n        \"BuyerBAN\": \"\",\n        \"CancelDate\": \"2021/04/06 16:47:37\"\n    }\n]"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/PosInvoiceEdits"}, "id": "38cba79f-9966-43c2-a190-681526b512ce", "name": "2. 作廢發票/折讓", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/json"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "InvoiceNo", "value": "TZ00000011"}, {"enabled": true, "type": "Text", "name": "InvoiceDate", "value": "2019/10/09 17:30:16"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/GetInvoiceStatus"}, "id": "01cd4c74-f1dc-42f2-8176-f0cb80e2072c", "name": "3. 查詢發票狀態(單筆)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "InvoiceNo", "value": "TZ00000011"}, {"enabled": true, "type": "Text", "name": "InvoiceDate", "value": "2019/10/09 17:30:16"}, {"enabled": true, "type": "Text", "name": "PosBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "${apiKey}"}]}, "bodyType": "Text", "textBody": "[\n  {\n    \"InvoiceNo\":\"TZ00000011\",\n    \"InvoiceDate\":\"2019/10/09 17:30:16\",\n    \"PosBAN\":\"83193989\",\n    \"ApiKey\":\"S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY\"\n  }\n]"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/GetInvoiceStatusS"}, "id": "c85894e0-ba56-4db9-ae75-9b85a808bb89", "name": "4. 查詢發票狀態(多筆)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/json"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "AllowanceNo", "value": "AB00000001"}, {"enabled": true, "type": "Text", "name": "AllowanceDate", "value": "2016/02/18 16:39:00"}, {"enabled": true, "type": "Text", "name": "SellerBAN", "value": "12656354"}, {"enabled": true, "type": "Text", "name": "PosBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "${apikey}"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/GetAllowanceStatusS"}, "id": "5f38b67e-6a67-4af7-a506-3058c10a3aee", "name": "5. 查詢折讓狀態(多筆)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "PosBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "${apikey}"}, {"enabled": true, "type": "Text", "name": "SellerBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "SellerApi<PERSON>ey", "value": "${apikey}"}, {"enabled": true, "type": "Text", "name": "Year", "value": "2021"}]}, "bodyType": "Form", "textBody": "{\n    \"PosBAN\": \"83193989\",\n    \"ApiKey\": \"S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY\",\n    \"SellerBAN\": \"83193989\",\n    \"SellerApiKey\": \"S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY\",\n    \"Year\": \"2020\"\n}"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/GetInvoiceNo"}, "id": "c493e344-481f-4d99-9437-27174cc4fef8", "name": "6. 取得發票大區間(財政部大平台配發)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "PosBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "${apikey}"}, {"enabled": true, "type": "Text", "name": "SellerBAN", "value": "${posBAN}"}, {"enabled": true, "type": "Text", "name": "InvoiceType", "value": "35"}, {"enabled": true, "type": "Text", "name": "Year", "value": "2021"}, {"enabled": true, "type": "Text", "name": "StartMonth", "value": "3"}, {"enabled": true, "type": "Text", "name": "EndMonth", "value": "4"}, {"enabled": true, "type": "Text", "name": "InvoiceTrack", "value": "AB"}, {"enabled": true, "type": "Text", "name": "InvoiceStart", "value": "00001000"}, {"enabled": true, "type": "Text", "name": "InvoiceEnd", "value": "00001999"}]}, "bodyType": "Text", "textBody": "[\n    {\n        \"PosBAN\": \"83193989\",\n        \"ApiKey\": \"S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY\",\n        \"SellerBAN\": \"83193989\",\n        \"InvoiceType\": \"35\",\n        \"Year\": \"2021\",\n        \"StartMonth\": \"3\",\n        \"EndMonth\": \"4\",\n        \"InvoiceTrack\": \"LF\",\n        \"InvoiceStart\": \"00000202\",\n        \"InvoiceEnd\": \"00000203\"\n    }\n]"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"name": "http", "version": "V11"}, "host": "${baseUrl}", "path": "/AddPosInvoiceRange"}, "id": "56fd3872-26a3-460c-9bc7-2b1bd15408ec", "name": "7. 新增發票中區間", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/json"}]}}]}], "environments": [{"id": "5700659f-6127-469a-9031-1fc796166f9d", "name": "金財通 omos", "variables": {"0ce68555-c41a-4ac1-a24c-9bc82f9873a1": {"createdAt": "2021-04-06T11:44:18.725+08:00", "name": "baseUrl", "value": "*************/SCMWebAPITest/api", "enabled": true, "private": false}, "59a064f8-80d4-4299-bf2e-398205d544f2": {"createdAt": "2021-04-06T12:35:47.526+08:00", "name": "posBAN", "value": "83193989", "enabled": true, "private": false}, "56448228-4cdc-4c04-9b14-5f5469fd5458": {"createdAt": "2021-04-06T12:35:47.526+08:00", "name": "apikey", "value": "S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY", "enabled": true, "private": false}, "29f434b5-09ac-45f2-ba40-f1cb3bfe9679": {"createdAt": "2021-04-06T13:46:46.342+08:00", "name": "host", "value": "*************", "enabled": true, "private": false}}}, {"id": "b0966f00-3e36-4a79-929b-494a85043c88", "name": "dev", "variables": {"c4a54d65-e750-4a30-8e58-09a8f22608ae": {"createdAt": "2021-02-22T17:49:52.214+08:00", "name": "baseUrl", "value": "dev-api-pos.omos.tw", "enabled": true, "private": false}, "e54584d9-fe94-4c4b-a735-9c8f4bff7550": {"createdAt": "2021-03-25T14:23:28.653+08:00", "name": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aW1lem9uZSI6IkFzaWEvVGFpcGVpIiwibGFzdF9sb2dpbiI6IjIwMjEtMDQtMDZUMDg6NDE6MjkiLCJyb2xlIjp7ImlkIjoxLCJuYW1lIjoiXHU1ZTk3XHU5NTc3In0sImJyYW5kX2lkIjoxLCJuYW1lIjoidW1fbWciLCJjaGFubmVsX2lkIjoxLCJjbGllbnRfaWQiOjEsImlkIjoxMSwic3lzX3R5cGUiOiJjYyIsImlhdCI6MTYxNzY5OTE1MCwiZXhwIjoxNjE3NzAyNzUwfQ.am_0kA-ykwKfL7HkVl2CWLa8nCLKhWyxaEGrNysjV4s", "enabled": true, "private": false}, "0dc3e07c-97ea-48a2-851a-6569bd821a47": {"createdAt": "2021-03-31T15:20:58.943+08:00", "name": "orderId", "value": "25", "enabled": true, "private": false}, "6af3b2d4-4b28-4bc8-9e60-825d94621207": {"createdAt": "2021-03-31T15:20:58.943+08:00", "name": "memberId", "value": "3", "enabled": true, "private": false}, "126b9762-2025-4028-a7af-b77b68ed673e": {"createdAt": "2021-04-04T15:51:22.402+08:00", "name": "couponId", "value": "8", "enabled": true, "private": false}, "877a6ade-53e6-4bb1-9ba3-9678e0142031": {"createdAt": "2021-04-04T15:51:22.402+08:00", "name": "storeAccount", "value": "1", "enabled": true, "private": false}}}, {"id": "ed062758-be78-4f77-a93e-a1729e22ed83", "name": "金財通 okshop", "variables": {"c9c5edba-e612-49a0-a111-fd2c7335c474": {"createdAt": "2021-04-06T18:31:30.931+08:00", "name": "posBAN", "value": "********", "enabled": true, "private": false}, "6f0a1f13-b2e7-4ec5-b147-7b00a4e15cbe": {"createdAt": "2021-04-06T18:31:37.828+08:00", "name": "apikey", "value": "LXTP2UV2-LXTP-LXTP-LXTP-LXTP2UV2IIWS", "enabled": true, "private": false}, "9d8257e0-bae2-4303-b8a6-fbaf47f332ef": {"createdAt": "2021-04-06T18:32:56.348+08:00", "name": "host", "value": "*************", "enabled": true, "private": false}, "bb7efb37-bebf-480a-8d34-610b7a3dca2c": {"createdAt": "2021-04-06T18:32:56.348+08:00", "name": "baseUrl", "value": "*************/SCMWebAPITest/api", "enabled": true, "private": false}}}, {"id": "edae5501-90f5-408e-b67a-9b1542acec6c", "name": "prod", "variables": {"09c3d1ff-74f6-4cf4-93bc-e769fa4202b3": {"createdAt": "2021-02-23T17:39:09.218+08:00", "name": "baseUrl", "value": "api-pos.omos.tw", "enabled": true, "private": false}}}]}