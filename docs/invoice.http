# var

@dev = https://webtest.bpscm.com.tw/SCMWEBAPI/API
@prod = https://www.bpscm.com.tw/SCMWebAPI/api
@host = {{dev}}
@apikey_prod = S6FR5FQE-S6FR-S6FR-S6FR-S6FR5FQE2SMA
@apikey_dev = S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY
@apikey = {{apikey_dev}}
@pos_ban = 83193989
@soap_dev = https://webtest.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL
@soap_prod = https://www.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL
@soap = {{soap_dev}}

### 取得發票號碼

POST {{soap}} HTTP/1.1
Content-Type: text/xml

<soap:Envelope
  xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetInvoiceRange xmlns="http://tempuri.org/">
        <machineNo>M1</machineNo>
        <APIKey>{{apikey}}</APIKey>
        <deptCode></deptCode>
        <invoiceYear>2021</invoiceYear>
        <MainBAN>{{pos_ban}}</MainBAN>
        <invoiceMonth>7</invoiceMonth>
        <sellerBAN>{{pos_ban}}</sellerBAN>
        <invoiceType>35</invoiceType>
    </GetInvoiceRange>
  </soap:Body>
</soap:Envelope>

### 開立發票/折讓

POST {{host}}/PosInvoiceNews HTTP/1.1
Content-Type: application/json

[
  {
    "PosBAN": "83193989",
    "ApiKey": "S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY",
    "SellerBAN": "90685329",
    "StoreCode": "",
    "StoreName": "",
    "RegisterCode": "",
    "OrderNo": "C20210602000028",
    "State": 1,
    "InvoiceNo": "**********",
    "InvoiceDate": "2021/06/02",
    "BuyerBAN": "",
    "PrintMark": "Y",
    "InvoiceType": "35",
    "SalesAmt": 220,
    "FreeTaxSalesAmt": 0,
    "ZeroTaxSalesAmt": 0,
    "TaxAmt": 0,
    "TotalAmt": 220,
    "TaxType": "1",
    "TaxRate": 0.05,
    "DiscountAmt": 0,
    "HealthyAmt": 0,
    "RandomNumber": "9664",
    "InvoiceDetails": [
      {
        "SequenceNo": "",
        "ItemName": "餐費",
        "Qty": 1,
        "UnitPricec": 220,
        "SalesAmt": 220,
        "TaxAmt": 0,
        "TotalAmt": 220,
        "DiscountAmt": 0,
        "HealthAmt": 0
      }
    ]
  }
]

### 作廢發票/折讓

POST {{host}}/PosInvoiceEdits HTTP/1.1
Content-Type: application/json

[
  {

  }
]

### 查詢發票狀態(單筆)

POST {{host}}/GetInvoiceStatus HTTP/1.1
Content-Type: application/json

{
  "InvoiceNo": "PV00000001",
  "InvoiceDate": "2021/08/18"
}

### 查詢發票狀態(多筆)(?)

POST {{host}}/GetInvoiceStatusS HTTP/1.1
Content-Type: application/json

[
  {
    "InvoiceNo": "AB00000001",
    "InvoiceDate": "2016/02/18 16:39:00",
    "PosBAN": "83193989",
    "ApiKey": "S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY"
  }
]

### 查詢折讓狀態(多筆)(?)

POST {{host}}/GetAllowanceStatusS HTTP/1.1
Content-Type: application/json

[
  {
    "AllowanceNo": "AB00000001",
    "AllowanceDate": "2016/02/18 16:39:00",
    "SellerBAN": "12656354",
    "PosBAN": "83193989",
    "ApiKey": "S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY"
  }
]

### 取得發票大區間(財政部大平台配發)(?)

POST {{host}}/GetInvoiceNo HTTP/1.1
Content-Type: application/json

{
  "PosBAN": "12656354",
  "ApiKey": "xxxxxx-xxxxx-xxxx-xxxx-xxxxxxxxxxxxxxxx",
  "SellerBAN": "12656354",
  "SellerApiKey": "xxxxxx-xxxxx-xxxx-xxxx-xxxxxxxxxxxxxxxx",
  "Year": 2018
}

### 新增發票中區間(O)

POST {{host}}/AddPosInvoiceRange HTTP/1.1
Content-Type: application/json

[
  {
    "PosBAN": "{{pos_ban}}",
    "ApiKey": "{{apikey}}",
    "SellerBAN": "{{pos_ban}}",
    "InvoiceType": "35",
    "Year": 2021,
    "StartMonth": 7,
    "EndMonth": 8,
    "InvoiceTrack": "PV",
    "InvoiceStart": "00003000",
    "InvoiceEnd": "00003999"
  }
]
