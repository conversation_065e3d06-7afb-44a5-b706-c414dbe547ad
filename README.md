# OKPay

* flutter version: 1.22.6

## 金財通

## 金財通後台

* ~~測試 - http://61.57.230.103/ASPTest/default.aspx~~
* 測試 - https://webtest.bpscm.com.tw/ASPTest
* 正式 - https://www.bpscm.com.tw/ASP/Login/Login.aspx

### omos

* 測試
  * 83193989POS
  * 0001
* 正式
  * 83193989POS
  * Posc@t666

## 測試用 API_KEY

* omos - S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY
* okshop - LXTP2UV2-LXTP-LXTP-LXTP-LXTP2UV2IIWS

## 正式用 API_KEY

* omos - S6FR5FQE-S6FR-S6FR-S6FR-S6FR5FQE2SMA

## tax no

* omos - 83193989
* okshop - 29187497

## 開立發票的 api 網址

* ~~測試站 - http://61.57.230.103/SCMWebAPITest/api/~~
* 測試站 - https://webtest.bpscm.com.tw/SCMWEBAPI/API
* 正式站 - https://www.bpscm.com.tw/SCMWebAPI/api/

## 取得發票的 api 網址

* 測試站 - http://61.57.230.103/WebService_Npoi2Test/PosInvoiceRange.asmx
* 正式站 - https://www.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL

## [doc](https://drive.google.com/drive/folders/1WWxfqnkllcdtjXiCQEV3kNt8UVS-p9hA?usp=sharing)

* [金財通_POS配號小區間初版.pdf](https://drive.google.com/file/d/1pmacosVWK-dkr2euz7D727cfG-xHkwjT/view?usp=sharing)

取得發票區間文件

* [金財通_POS電子發票傳輸格式json-v0.9.pdf](https://drive.google.com/file/d/1jOpsAz2mNrkmTvyopvMvGdm1GxRatoc3/view?usp=sharing)

使用 api 的方式開立發票

* [金財通_POS電子發票傳輸格式txt-v1.4單純上傳.pdf](https://drive.google.com/file/d/15DyHObkRsync23hjg_eJ-zlzO1e0bywP/view?usp=sharing)

使用上傳 txt 文字檔到 ftp 的方式開立發票

```
我剛測試有結論了

譬如 國稅局->金財通 1,000 張
********** ~ **********

POS 機台設定每次分配張數 200
程式這邊只能取 5 次，依序為

1. ********** ~ **********
2. ********** ~ **********
3. ********** ~ **********
4. ********** ~ **********
5. ********** ~ **********
6. 錯誤訊息: 所有的發票區間內發票號碼已被取用完畢
```

## QRCode AES encrypt

* 測試字串 - AA1234567810412191234000000b4000000b40000000054185095dSypnr83S3oOPU5HiEx49w==
  * AA12345678 - 發票號碼
  * 1041219 - 日期
  * 1234 - 隨機碼
  * 000000b4 - 銷售額
  * 000000b4 - 總額
  * 00000000 - 買方
  * 54185095 - 賣方
  * dSypnr83S3oOPU5HiEx49w== - 加密字串 (AA123456781234 使用 AES 加密)
* 密碼種子 - 12345678
* 32碼金鑰 - 6647A889B4B5912BECB5D01065CCD670
* iv - Dt8lyToo17X/XkXaQvihuA==

1. 產生加密金鑰

從財政部國稅局網站下載 tool/genKey.sh

* 範例
  * 種子密碼 - 12345678 (passphrase)
  * 加密金鑰 - 6647A889B4B5912BECB5D01065CCD670 (base16)

* 微方科技有限公司 (29187497)
  * 種子密碼 - 29187497
  * 加密金鑰 - 1C020EBCDFC61E624D7EAE12F146D750

* 跨界創新 (83193989)
  * 種子密碼 - 83193989
  * 加密金鑰 - C30F44D5C43C4ACA5BC83099C09E9972

2. [登入財政部電子發票整合服務平台](https://www.einvoice.nat.gov.tw/)設定種子密碼

* 微方科技有限公司 (29187497)
  * 29187497
  * qaz1wsx2

* 跨界創新 (83193989)
  * 83193989
  * 1qaz2wsx

## ref

* [這張發票透露了你的什麼訊息？](https://medium.com/life-of-small-data-engineer/your-informations-in-einvoice-49c5a7ceb567)
* [電子發票-TurnKey用XML相關參考](https://dotblogs.com.tw/programmerfighting/2018/07/26/163310)
* [自建 Turnkey](https://www.ares.com.tw/products/egui/faq/)
* [取得電子發票QRCODE中AES加密金鑰](https://pjchender.blogspot.com/2015/07/qrcodeaes.html)
* [國稅局網站設定及驗證](http://www.hserp.tw/xfaq/?ht_kb=%E9%9B%BB%E5%AD%90%E7%99%BC%E7%A5%A8qrcode-32%E4%BD%8D%E5%85%83%E5%8A%A0%E5%AF%86%E9%87%91%E9%91%B0%E7%94%A2%E7%94%9F%E6%96%B9%E5%BC%8F)
* [QR碼組成](https://www.ecpay.com.tw/Announcement/DetailAnnouncement?nID=3197)
* [電子發票整合服務平台 - 加解密 API 使用說明書 V1.12](https://www.einvoice.nat.gov.tw/EINSM/ein_upload/html/ENV/*************-1.html)

### 參考程式

* [QRCodeAESInNodejs.md](https://gist.github.com/WJWang/1855ce92c2b1bac1313e1d1484297926)
* [電子發票 QR碼產生用程式碼 C# .NET](https://radio-idea.blogspot.com/2018/08/qr-c-net.html)

## 測試機帳號

* 集團代號(client): omos
* 通路代號(channel): zhongxiao
* 員工帳號(store account): um_mg
* 員工密碼: a

## build

settings.json 沒有加上 --flavor=production 都是 development

```json
"args": [
    "--flavor=production",
]
```
