import 'package:flutter_test/flutter_test.dart';

void main() {
  group('pad', () {
    test('padLeft1', () {
      final actual = '1'.padLeft(4, '0');
      const matcher = '0001';
      expect(actual, matcher);
    });

    test('padLeft4', () {
      final actual = '1234'.padLeft(4, '0');
      const matcher = '1234';
      expect(actual, matcher);
    });

    test('padLeft5', () {
      final actual = '12345'.padLeft(4, '0');
      const matcher = '12345';
      expect(actual, matcher);
    });
  });
}
