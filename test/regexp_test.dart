import 'package:flutter_test/flutter_test.dart';

void main() {
  group('hello', () {
    RegExp regExp = RegExp(r'hello', caseSensitive: false);
    test('contains', () {
      final actual = regExp.hasMatch('Hello, world!');
      expect(actual, true);
    });

    test('does not contain', () {
      final actual = regExp.hasMatch('How are you?');
      expect(actual, false);
    });
  });

  test('order number', () {
      RegExp regExp = RegExp('151', caseSensitive: false);
      final actual = regExp.hasMatch('C20210315000151');
      expect(actual, true);
    });
}
