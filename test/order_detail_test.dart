import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:guests/app/models/order_detail_model.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    print('[main] setUpAll: main scope 只執行一次');
  });

  setUp(() {
    print('[main] setUp: scope 及 子scope 每一次 test 都會執行一次');
  });

  test('create time', () async {
    const jsonFile = 'assets/models/order_detail.json';
    print(jsonFile);
    final jsonString =
        await rootBundle.loadString('assets/models/order_detail.json');
    print(jsonString);
    expect(jsonString.isNotEmpty, true);
  });

  test('decode json string', () async {
    const jsonFile = 'assets/models/order_detail.json';
    print(jsonFile);
    final jsonString =
        await rootBundle.loadString('assets/models/order_detail.json');
    print(jsonString);
    final json = jsonDecode(jsonString);
    final od = OrderDetail.fromJson(json);
    print('$od');
  });
}
