import 'package:flutter_test/flutter_test.dart';
import 'package:guests/app/models/invoice_model.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';

void main() {
  final invoiceDate = DateTime(2023, 1, 26);
  group('print mark false', () {
    final invoice = InvoiceModel(
      printMark: 0,
    );
    test('print mark', () {
      expect(invoice.testIsPrinted, false);
    });
    test('display print mark', () {
      expect(invoice.displayPrintMark, '電子發票證明聯');
    });
    test('font size', () {
      expect(invoice.printMarkFontSize, 36.0);
    });
  });

  group('print mark true', () {
    final invoice = InvoiceModel(
      printMark: 1,
    );
    test('print mark', () {
      expect(invoice.testIsPrinted, true);
    });
    test('display print mark', () {
      expect(invoice.displayPrintMark, '電子發票證明聯補印');
    });
    test('font size', () {
      expect(invoice.printMarkFontSize, 30.0);
    });
  });

  group('date time', () {
    final invoice = InvoiceModel(
      invoiceDate: invoiceDate.millisecondsSinceEpoch,
    );
    test('date time', () {
      expect(invoice.dateTime, invoiceDate);
    });
    test('display date time', () {
      expect(invoice.displayDateTime, invoiceDate.yMdHms);
    });
    test('display tw date time', () {
      expect(invoice.displayTwDateTime, '112年01-02月');
    });
  });

  test('custom field', () {
    final invoice = InvoiceModel();
    expect(invoice.testCustomField, '**********');
  });

  group('random number', () {
    final invoice = InvoiceModel(
      randomNumber: '0123',
    );
    test('display random number', () {
      expect(invoice.displayRandomNumber, '隨機碼 0123');
    });
    test('random number string', () {
      expect(invoice.randomNumber, '0123');
    });
  });

  test('testGetEncryptedString', () {
    final invoice = InvoiceModel(
      invoiceNumber: 'AA12345678',
      randomNumber: '1234',
    );
    final actual = invoice.testGetEncryptedString(kAESKey);
    final matcher = 'vt2qXrGN7o/Xi2r5W1uADw==';
    expect(actual, matcher);
  });

  test('testGetEncryptedString2', () {
    final invoice = InvoiceModel(
      invoiceNumber: 'AA12345678',
      randomNumber: '1234',
    );
    final aesKey = '6647A889B4B5912BECB5D01065CCD670';
    final actual = invoice.testGetEncryptedString(aesKey);
    final matcher = 'dSypnr83S3oOPU5HiEx49w==';
    expect(actual, matcher);
  });

  test('left qr string', () {
    final invoiceNumber = 'AA12345678';
    final randomNumber = '1234';
    final seller = '83193989';
    final buyer = '12345678';
    final invoice = InvoiceModel(
      invoiceNumber: invoiceNumber,
      randomNumber: randomNumber,
      buyerIdentifier: buyer,
      sellerIdentifier: seller,
      invoiceDate: DateTime(2017, 12, 1, 18, 32, 1).millisecondsSinceEpoch,
    );
    final encrypted = invoice.testGetEncryptedString(kAESKey);
    final matcher =
        '${invoiceNumber}1061201${randomNumber}000003B8000003E8$buyer$seller$encrypted';
    // FIXME:
    // final actual = invoice.leftQrString;
    // expect(actual, matcher);
  });

  group('seller & buyer', () {
    final seller = '83193989';
    final buyer = '12345678';
    final invoice = InvoiceModel(
      buyerIdentifier: buyer,
      sellerIdentifier: seller,
    );
    test('display seller', () {
      expect(invoice.displaySeller, '賣方$seller');
    });

    test('display buyer', () {
      expect(invoice.displayBuyer, '買方$buyer');
    });

    test('contains buyer', () {
      expect(invoice.hasBuyer, true);
    });

    test('buyer identifier', () {
      expect(invoice.testBuyerIdentifier, buyer);
    });
  });

  test('invoice number', () {
    final invoice = InvoiceModel(
      invoiceNumber: 'AB12345678',
    );
    expect(invoice.displayInvoiceNumber, 'AB-12345678');
  });

  test('bar code', () {
    final invoice = InvoiceModel(
      invoiceNumber: 'AB12345678',
      randomNumber: '1234',
      invoiceDate: invoiceDate.millisecondsSinceEpoch,
    );
    expect(invoice.barcode, '11202AB123456781234');
  });

  test('right qr string', () {
    // TODO:
  });

  group('item', () {
    final invoice = InvoiceModel(
      productName: kDefaultItemName,
    );
    test('item name', () {
      expect(invoice.itemName, kDefaultItemName);
    });
    test('item count', () {
      expect(invoice.itemCount, '1');
    });

    test('private item count', () {
      expect(invoice.testItemCount, '1');
    });

    test('item price', () {
      // TODO:
    });
  });

  test('string encode', () {
    final invoice = InvoiceModel();
    expect(invoice.testStringEncode, '1');
  });

  test('total hex without tax', () {
    // TODO:
  });

  test('total amount', () {
    final invoice = InvoiceModel(totalAmount: 1234);
    expect(invoice.displayTotalAmount, '總計 1,234');
  });
}
