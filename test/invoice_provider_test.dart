import 'package:flutter_test/flutter_test.dart';
import 'package:guests/app/providers/invoice_provider.dart';

void main() {
  group('description', () {
    test('create random number', () {
      final random1 = InvoiceProvider.createRandomNumber();
      final random2 = InvoiceProvider.createRandomNumber();
      final actual = random1 != random2;
      final matcher = true;
      expect(actual, matcher);
    });
  });
}
