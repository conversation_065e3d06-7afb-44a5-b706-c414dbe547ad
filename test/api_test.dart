import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'api_test.mocks.dart';

@GenerateMocks([Client])
main() {
  group('ApiProvider', () {
    test('returns an Album if the http call completes successfully', () async {
      final client = MockClient();

      // Use Mockito to return a successful response when it calls the
      // provided http.Client.
      when(client
              .get(Uri.parse('https://jsonplaceholder.typicode.com/albums/1')))
          .thenAnswer((_) async =>
              Response('{"userId": 1, "id": 2, "title": "mock"}', 200));

      // expect(await fetchAlbum(client), isA<Album>());
    });
    // test('returns a Post if the http call completes successfully', () async {
    //   final client = MockClient();

    //   // Use Mockito to return a successful response when it calls the
    //   // provided http.Client.
    //   when(client.get('https://jsonplaceholder.typicode.com/posts/1'))
    //       .thenAnswer((_) async => Response('{"title": "Test"}', 200));

    //   expect(await fetchPost(client), isA<Post>());
    // });

    // test('throws an exception if the http call completes with an error', () {
    //   final client = MockClient();

    //   // Use Mockito to return an unsuccessful response when it calls the
    //   // provided http.Client.
    //   when(client.get('https://jsonplaceholder.typicode.com/posts/1'))
    //       .thenAnswer((_) async => Response('Not Found', 404));

    //   expect(fetchPost(client), throwsException);
    // });
  });
}
