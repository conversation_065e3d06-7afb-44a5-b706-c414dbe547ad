import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:guests/app/models/order_creating_model.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('order creating model', () {
    test('load model', () async {
      const jsonFile = 'assets/models/order_creating.json';
      print(jsonFile);
      final jsonString =
          await rootBundle.loadString('assets/models/order_creating.json');
      print(jsonString);
      final json = jsonDecode(jsonString);
      final od = OrderCreating.fromJson(json);
      print('$od');
    });
  });
}
