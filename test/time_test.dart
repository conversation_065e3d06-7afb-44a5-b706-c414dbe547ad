import 'package:flutter_test/flutter_test.dart';
import 'package:guests/extension.dart';

void main() {
  final dtString = "2021-02-19T01:56:02";
  group('parse date time string', () {
    test('is utc', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.isUtc, true);
    });
    test('is NOT utc', () {
      final dt = DateTime.parse(dtString);
      expect(dt.isUtc, false);
    });
    test('utc', () {
      final actual = utcToLocal(dtString);
      final matcher = DateTime.utc(2021, 2, 19, 1, 56, 2).toLocal();
      expect(actual, matcher);
    });
    test('utc', () {
      final actual = DateTime.parse('${dtString}z');
      final matcher = DateTime.utc(2021, 2, 19, 1, 56, 2);
      expect(actual, matcher);
    });
    test('local', () {
      final actual = DateTime.parse(dtString);
      final matcher = DateTime(2021, 2, 19, 1, 56, 2);
      expect(actual, matcher);
    });
  });

  group('date time extension', () {
    test('MMddHHmm', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.mmddHHmm, '02/19 01:56');
    });
    test('yMdHms', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.yMdHms, '2021/02/19 01:56:02');
    });
    test('yMd', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.yMd, '2021/02/19');
    });
    test('1 digit day', () {
      final dt = DateTime(2021, 2, 1);
      expect(dt.dd, '01');
    });
    test('2 digit day', () {
      final dt = DateTime(2021, 2, 19);
      expect(dt.dd, '19');
    });
    test('yyyy', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.yyyy, '2021');
    });
    test('twYear', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.twYear, '110');
    });
    test('1 digit month', () {
      final dt = DateTime(2021, 2);
      expect(dt.mm, '02');
    });
    test('2 digit month', () {
      final dt = DateTime(2021, 12);
      expect(dt.mm, '12');
    });
    test('twYYYMMdd', () {
      final dt = DateTime.parse('${dtString}z');
      expect(dt.twYYYMMdd, '1100219');
    });
  });

  group('timestamp', () {
    test('today', () {
      const millisecondsPerDay = 24 * 60 * 60 * 1000;
      final now = DateTime.now();
      final daysSinceEpoch = now.millisecondsSinceEpoch ~/ millisecondsPerDay;
      final actual = daysSinceEpoch * millisecondsPerDay;
      final today = DateTime(now.year, now.month, now.day);
      final matcher = today.millisecondsSinceEpoch;
      expect(actual, matcher);
    });

    test('day 2', () {
      const millisecondsPerDay = 24 * 60 * 60 * 1000;
      final now = DateTime(1970, 1, 2);
      final daysSinceEpoch = now.millisecondsSinceEpoch ~/ millisecondsPerDay;
      final actual = daysSinceEpoch * millisecondsPerDay;
      final today = DateTime(now.year, now.month, now.day);
      final matcher = today.millisecondsSinceEpoch;
      expect(actual, matcher);
    });

    test('utc timestamp', () {
      final gmt = DateTime(1970, 1, 2);
      final utc = DateTime.utc(1970, 1, 2);
      // gmt 結果會少 8 小時
      expect(gmt.millisecondsSinceEpoch, utc.millisecondsSinceEpoch);
    });
  });
}
