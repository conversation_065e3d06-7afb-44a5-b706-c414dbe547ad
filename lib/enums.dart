// 金財通發票狀態
enum BpscmInvoiceStatus {
  Unknown, // 0: 折讓開立
  Invoice, // 1: 開立
  Cancel, // 2: 作廢
  Max,
}

extension ExtensionBpscmInvoiceStatus on BpscmInvoiceStatus {
  String get name {
    switch (this) {
      case BpscmInvoiceStatus.Unknown:
        return '折讓開立';
      case BpscmInvoiceStatus.Invoice:
        return '開立';
      case BpscmInvoiceStatus.Cancel:
        return '作廢';
      default:
        return '';
    }
  }

  bool get isUnknown => BpscmInvoiceStatus.Unknown == this;
  bool get isInvoice => BpscmInvoiceStatus.Invoice == this;
  bool get isCancel => BpscmInvoiceStatus.Cancel == this;
}

// OKpay 發票狀態
enum InvoiceStatus {
  Invoice, // 0: 開立
  Cancel, // 1: 作廢
  Discount, // 2: 折讓
  Max,
}

extension ExtensionInvoiceStatus on InvoiceStatus {
  String get name {
    switch (this) {
      case InvoiceStatus.Invoice:
        return '開立';
      case InvoiceStatus.Cancel:
        return '作廢';
      case InvoiceStatus.Discount:
        return '折讓';
      default:
        return '';
    }
  }

  bool get isInvoice => InvoiceStatus.Invoice == this;
  bool get isCancel => InvoiceStatus.Cancel == this;
  bool get isDiscount => InvoiceStatus.Discount == this;
}

// 按鈕
enum Button {
  Negative, // 0: 取消
  Positive, // 1: 確認
  Middle, // 2:
  Max,
}

extension ExtensionButton on Button {
  bool get isNegative => Button.Negative == this;
  bool get isPositive => Button.Positive == this;
  bool get isMiddle => Button.Middle == this;
}

// 訂單狀態
enum OrderStatus {
  Padding, // 0: 處理中(Creating)
  Accepted, // 1: 已確認(Created)
  Completed, // 2: 訂單完成
  Canceled, // 3: 訂單取消
  Exception, // 4: 訂單異常
  Refunded, // 5: 訂單退貨、退款
  Max,
}

extension ExtensionOrderStatus on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.Padding:
        return '處理中';
      case OrderStatus.Accepted:
        return '已確認';
      case OrderStatus.Completed:
        return '訂單完成';
      case OrderStatus.Canceled:
        return '訂單取消';
      case OrderStatus.Exception:
        return '訂單異常';
      case OrderStatus.Refunded:
        return '訂單退貨、退款';
      default:
        return '';
    }
  }

  bool get isPadding => OrderStatus.Padding == this;
  bool get isAccepted => OrderStatus.Accepted == this;
  bool get isCompleted => OrderStatus.Completed == this;
  bool get isCanceled => OrderStatus.Canceled == this;
  bool get isException => OrderStatus.Exception == this;
  bool get isRefunded => OrderStatus.Refunded == this;
}

// 商品型態
enum ItemType {
  Normal, // 0: 一般
  PreOrder, // 1: 預購
  OneMore, // 2: 加購
  Gift, // 3: 贈品
  Additional, // 4: 集客多專用額外費用
}

extension ExtensionItemType on ItemType {
  String get name {
    switch (this) {
      case ItemType.Normal:
        return '一般';
      case ItemType.PreOrder:
        return '預購';
      case ItemType.OneMore:
        return '加購';
      case ItemType.Gift:
        return '贈品';
      case ItemType.Additional:
        return '集客多專用額外費用';
      default:
        return '';
    }
  }
}

// 角色
enum StoreRole {
  None,
  Boss, // 1: 店長
  Employee, // 2: 店員
  Max,
}

extension ExtensionRole on StoreRole {
  String get name {
    switch (this) {
      case StoreRole.Boss:
        return '店長';
      case StoreRole.Employee:
        return '店員';
      default:
        return '';
    }
  }

  bool get isBoss => StoreRole.Boss == this;

  bool get isEmployee => StoreRole.Employee == this;

  bool get isAny {
    switch (this) {
      case StoreRole.Boss:
      case StoreRole.Employee:
        return true;
      default:
        return false;
    }
  }
}

// 開關
enum Switcher {
  Off, // 0: 關閉
  On, // 1: 開啟
  Max,
}

extension ExtensionSwitcher on Switcher {
  bool get isOff => Switcher.Off == this;
  bool get isOn => Switcher.On == this;
  String get name {
    switch (this) {
      case Switcher.Off:
        return '關閉';
      case Switcher.On:
        return '開啟';
      default:
        return '';
    }
  }
}

// 稅率類型
enum TaxType {
  Taxable, // 應稅：5%稅率 (value: 1)
  TaxFree, // 免稅(農產品) (value: 3)
}

extension ExtensionTaxType on TaxType {
  String get displayName {
    switch (this) {
      case TaxType.Taxable:
        return '應稅：5%稅率';
      case TaxType.TaxFree:
        return '免稅(農產品)';
    }
  }

  int get value {
    switch (this) {
      case TaxType.Taxable:
        return 1;
      case TaxType.TaxFree:
        return 3;
    }
  }

  static TaxType? fromValue(int value) {
    switch (value) {
      case 1:
        return TaxType.Taxable;
      case 3:
        return TaxType.TaxFree;
      default:
        return null;
    }
  }

  bool get isTaxable => TaxType.Taxable == this;
  bool get isTaxFree => TaxType.TaxFree == this;
}
