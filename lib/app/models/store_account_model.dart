// To parse this JSON data, do
//
//     final storeAccount = storeAccountFromJson(jsonString);

import 'dart:convert';

class StoreAccount {
  StoreAccount({
    this.brandId,
    this.channelId,
    this.clientId,
    this.comment,
    this.createdAt,
    this.id,
    this.lastLogin,
    this.name,
    this.role,
    this.status,
    this.updatedAt,
    this.username,
  });

  num? brandId;
  num? channelId;
  num? clientId;
  String? comment;
  String? createdAt;
  num? id;
  String? lastLogin;
  String? name;
  Role? role;
  num? status;
  String? updatedAt;
  String? username;

  StoreAccount copyWith({
    num? brandId,
    num? channelId,
    num? clientId,
    String? comment,
    String? createdAt,
    num? id,
    String? lastLogin,
    String? name,
    Role? role,
    num? status,
    String? updatedAt,
    String? username,
  }) =>
      StoreAccount(
        brandId: brandId ?? this.brandId,
        channelId: channelId ?? this.channelId,
        clientId: clientId ?? this.clientId,
        comment: comment ?? this.comment,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        lastLogin: lastLogin ?? this.lastLogin,
        name: name ?? this.name,
        role: role ?? this.role,
        status: status ?? this.status,
        updatedAt: updatedAt ?? this.updatedAt,
        username: username ?? this.username,
      );

  factory StoreAccount.fromRawJson(String str) =>
      StoreAccount.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StoreAccount.fromJson(Map<String, dynamic> json) => StoreAccount(
        brandId: json["brand_id"],
        channelId: json["channel_id"],
        clientId: json["client_id"],
        comment: json["comment"],
        createdAt: json["created_at"],
        id: json["id"],
        lastLogin: json["last_login"],
        name: json["name"],
        role: json["role"] == null ? null : Role.fromJson(json["role"]),
        status: json["status"],
        updatedAt: json["updated_at"],
        username: json["username"],
      );

  Map<String, dynamic> toJson() => {
        "brand_id": brandId,
        "channel_id": channelId,
        "client_id": clientId,
        "comment": comment,
        "created_at": createdAt,
        "id": id,
        "last_login": lastLogin,
        "name": name,
        "role": role?.toJson(),
        "status": status,
        "updated_at": updatedAt,
        "username": username,
      };
}

class Role {
  Role({
    this.id,
    this.name,
  });

  num? id;
  String? name;

  Role copyWith({
    num? id,
    String? name,
  }) =>
      Role(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory Role.fromRawJson(String str) => Role.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
