import 'dart:convert';

class InvoiceSettings {
  bool? invoiceEnabled;
  bool? invoiceSkipped;
  String? itemName;
  int? taxType;

  InvoiceSettings({
    this.invoiceEnabled,
    this.invoiceSkipped,
    this.itemName,
    this.taxType,
  });

  InvoiceSettings copyWith({
    bool? invoiceEnabled,
    bool? invoiceSkipped,
    String? itemName,
    int? taxType,
  }) =>
      InvoiceSettings(
        invoiceEnabled: invoiceEnabled ?? this.invoiceEnabled,
        invoiceSkipped: invoiceSkipped ?? this.invoiceSkipped,
        itemName: itemName ?? this.itemName,
        taxType: taxType ?? this.taxType,
      );

  factory InvoiceSettings.fromRawJson(String str) =>
      InvoiceSettings.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InvoiceSettings.fromJson(Map<String, dynamic> json) =>
      InvoiceSettings(
        invoiceEnabled: json["invoice_enabled"],
        invoiceSkipped: json["invoice_skipped"],
        itemName: json["item_name"],
        taxType: json["tax_type"],
      );

  Map<String, dynamic> toJson() => {
        "invoice_enabled": invoiceEnabled,
        "invoice_skipped": invoiceSkipped,
        "item_name": itemName,
        "tax_type": taxType,
      };
}
