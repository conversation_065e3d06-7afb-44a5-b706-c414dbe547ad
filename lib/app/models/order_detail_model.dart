// To parse this JSON data, do
//
//     final orderDetail = orderDetailFromJson(jsonString);

import 'dart:convert';

class OrderDetail {
  OrderDetail({
    this.storeAccountName,
    this.createdAt,
    this.id,
    this.memberName,
    this.orderNumber,
    this.paymentStatus,
    this.status,
    this.total,
    this.updatedAt,
    this.coupon,
    this.pointsLog,
    this.memberPhone,
    this.redeemMemberPoints,
    this.promotionCoupons,
    this.invoice,
    this.orderDiscount,
    this.orderItems,
    this.orderPayment,
  });

  String? storeAccountName;
  String? createdAt;
  num? id;
  String? memberName;
  String? orderNumber;
  num? paymentStatus;
  num? status;
  num? total;
  String? updatedAt;
  Coupon? coupon;
  PointsLog? pointsLog;
  String? memberPhone;
  num? redeemMemberPoints;
  List<PromotionCoupon>? promotionCoupons;
  Invoice? invoice;
  List<OrderDiscount>? orderDiscount;
  List<OrderItem>? orderItems;
  OrderPayment? orderPayment;

  OrderDetail copyWith({
    String? storeAccountName,
    String? createdAt,
    num? id,
    String? memberName,
    String? orderNumber,
    num? paymentStatus,
    num? status,
    num? total,
    String? updatedAt,
    Coupon? coupon,
    PointsLog? pointsLog,
    String? memberPhone,
    num? redeemMemberPoints,
    List<PromotionCoupon>? promotionCoupons,
    Invoice? invoice,
    List<OrderDiscount>? orderDiscount,
    List<OrderItem>? orderItems,
    OrderPayment? orderPayment,
  }) =>
      OrderDetail(
        storeAccountName: storeAccountName ?? this.storeAccountName,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        memberName: memberName ?? this.memberName,
        orderNumber: orderNumber ?? this.orderNumber,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        status: status ?? this.status,
        total: total ?? this.total,
        updatedAt: updatedAt ?? this.updatedAt,
        coupon: coupon ?? this.coupon,
        pointsLog: pointsLog ?? this.pointsLog,
        memberPhone: memberPhone ?? this.memberPhone,
        redeemMemberPoints: redeemMemberPoints ?? this.redeemMemberPoints,
        promotionCoupons: promotionCoupons ?? this.promotionCoupons,
        invoice: invoice ?? this.invoice,
        orderDiscount: orderDiscount ?? this.orderDiscount,
        orderItems: orderItems ?? this.orderItems,
        orderPayment: orderPayment ?? this.orderPayment,
      );

  factory OrderDetail.fromRawJson(String str) =>
      OrderDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderDetail.fromJson(Map<String, dynamic> json) => OrderDetail(
        storeAccountName: json["store_account_name"],
        createdAt: json["created_at"],
        id: json["id"],
        memberName: json["member_name"],
        orderNumber: json["order_number"],
        paymentStatus: json["payment_status"],
        status: json["status"],
        total: json["total"],
        updatedAt: json["updated_at"],
        coupon: json["coupon"] == null ? null : Coupon.fromJson(json["coupon"]),
        pointsLog: json["points_log"] == null
            ? null
            : PointsLog.fromJson(json["points_log"]),
        memberPhone: json["member_phone"],
        redeemMemberPoints: json["redeem_member_points"],
        promotionCoupons: json["promotion_coupons"] == null
            ? []
            : List<PromotionCoupon>.from(json["promotion_coupons"]
                .map((x) => PromotionCoupon.fromJson(x))),
        invoice:
            json["invoice"] == null ? null : Invoice.fromJson(json["invoice"]),
        orderDiscount: json["order_discount"] == null
            ? []
            : List<OrderDiscount>.from(
                json["order_discount"].map((x) => OrderDiscount.fromJson(x))),
        orderItems: json["order_items"] == null
            ? []
            : List<OrderItem>.from(
                json["order_items"].map((x) => OrderItem.fromJson(x))),
        orderPayment: json["order_payment"] == null
            ? null
            : OrderPayment.fromJson(json["order_payment"]),
      );

  Map<String, dynamic> toJson() => {
        "store_account_name": storeAccountName,
        "created_at": createdAt,
        "id": id,
        "member_name": memberName,
        "order_number": orderNumber,
        "payment_status": paymentStatus,
        "status": status,
        "total": total,
        "updated_at": updatedAt,
        "coupon": coupon?.toJson(),
        "points_log": pointsLog?.toJson(),
        "member_phone": memberPhone,
        "redeem_member_points": redeemMemberPoints,
        "promotion_coupons": promotionCoupons == null
            ? []
            : List<dynamic>.from(promotionCoupons!.map((x) => x.toJson())),
        "invoice": invoice?.toJson(),
        "order_discount": orderDiscount == null
            ? []
            : List<dynamic>.from(orderDiscount!.map((x) => x.toJson())),
        "order_items": orderItems == null
            ? []
            : List<dynamic>.from(orderItems!.map((x) => x.toJson())),
        "order_payment": orderPayment?.toJson(),
      };
}

class Coupon {
  Coupon({
    this.title,
    this.promotionType,
    this.discount,
  });

  String? title;
  num? promotionType;
  num? discount;

  Coupon copyWith({
    String? title,
    num? promotionType,
    num? discount,
  }) =>
      Coupon(
        title: title ?? this.title,
        promotionType: promotionType ?? this.promotionType,
        discount: discount ?? this.discount,
      );

  factory Coupon.fromRawJson(String str) => Coupon.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Coupon.fromJson(Map<String, dynamic> json) => Coupon(
        title: json["title"],
        promotionType: json["promotion_type"],
        discount: json["discount"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "promotion_type": promotionType,
        "discount": discount,
      };
}

class Invoice {
  Invoice({
    this.randomNumber,
    this.invoicePaper,
    this.vatNumber,
    this.carrierType,
    this.carrierId,
    this.npoBan,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.number,
  });

  String? randomNumber;
  bool? invoicePaper;
  String? vatNumber;
  num? carrierType;
  String? carrierId;
  String? npoBan;
  num? status;
  String? createdAt;
  String? updatedAt;
  String? number;

  Invoice copyWith({
    String? randomNumber,
    bool? invoicePaper,
    String? vatNumber,
    num? carrierType,
    String? carrierId,
    String? npoBan,
    num? status,
    String? createdAt,
    String? updatedAt,
    String? number,
  }) =>
      Invoice(
        randomNumber: randomNumber ?? this.randomNumber,
        invoicePaper: invoicePaper ?? this.invoicePaper,
        vatNumber: vatNumber ?? this.vatNumber,
        carrierType: carrierType ?? this.carrierType,
        carrierId: carrierId ?? this.carrierId,
        npoBan: npoBan ?? this.npoBan,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        number: number ?? this.number,
      );

  factory Invoice.fromRawJson(String str) => Invoice.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Invoice.fromJson(Map<String, dynamic> json) => Invoice(
        randomNumber: json["random_number"],
        invoicePaper: json["invoice_paper"],
        vatNumber: json["vat_number"],
        carrierType: json["carrier_type"],
        carrierId: json["carrier_id"],
        npoBan: json["npo_ban"],
        status: json["status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        number: json["number"],
      );

  Map<String, dynamic> toJson() => {
        "random_number": randomNumber,
        "invoice_paper": invoicePaper,
        "vat_number": vatNumber,
        "carrier_type": carrierType,
        "carrier_id": carrierId,
        "npo_ban": npoBan,
        "status": status,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "number": number,
      };
}

class OrderDiscount {
  OrderDiscount({
    this.type,
    this.discountId,
    this.discountName,
    this.discountDescription,
    this.discountPrice,
    this.storeAccountId,
  });

  num? type;
  num? discountId;
  String? discountName;
  String? discountDescription;
  num? discountPrice;
  num? storeAccountId;

  OrderDiscount copyWith({
    num? type,
    num? discountId,
    String? discountName,
    String? discountDescription,
    num? discountPrice,
    num? storeAccountId,
  }) =>
      OrderDiscount(
        type: type ?? this.type,
        discountId: discountId ?? this.discountId,
        discountName: discountName ?? this.discountName,
        discountDescription: discountDescription ?? this.discountDescription,
        discountPrice: discountPrice ?? this.discountPrice,
        storeAccountId: storeAccountId ?? this.storeAccountId,
      );

  factory OrderDiscount.fromRawJson(String str) =>
      OrderDiscount.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderDiscount.fromJson(Map<String, dynamic> json) => OrderDiscount(
        type: json["type"],
        discountId: json["discount_id"],
        discountName: json["discount_name"],
        discountDescription: json["discount_description"],
        discountPrice: json["discount_price"],
        storeAccountId: json["store_account_id"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "discount_id": discountId,
        "discount_name": discountName,
        "discount_description": discountDescription,
        "discount_price": discountPrice,
        "store_account_id": storeAccountId,
      };
}

class OrderItem {
  OrderItem({
    this.finalPrice,
    this.isChangePrice,
    this.productName,
    this.storeAccountId,
    this.type,
  });

  num? finalPrice;
  num? isChangePrice;
  String? productName;
  num? storeAccountId;
  num? type;

  OrderItem copyWith({
    num? finalPrice,
    num? isChangePrice,
    String? productName,
    num? storeAccountId,
    num? type,
  }) =>
      OrderItem(
        finalPrice: finalPrice ?? this.finalPrice,
        isChangePrice: isChangePrice ?? this.isChangePrice,
        productName: productName ?? this.productName,
        storeAccountId: storeAccountId ?? this.storeAccountId,
        type: type ?? this.type,
      );

  factory OrderItem.fromRawJson(String str) =>
      OrderItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderItem.fromJson(Map<String, dynamic> json) => OrderItem(
        finalPrice: json["final_price"],
        isChangePrice: json["is_change_price"],
        productName: json["product_name"],
        storeAccountId: json["store_account_id"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "final_price": finalPrice,
        "is_change_price": isChangePrice,
        "product_name": productName,
        "store_account_id": storeAccountId,
        "type": type,
      };
}

class OrderPayment {
  OrderPayment({
    this.info,
    this.total,
  });

  String? info;
  num? total;

  OrderPayment copyWith({
    String? info,
    num? total,
  }) =>
      OrderPayment(
        info: info ?? this.info,
        total: total ?? this.total,
      );

  factory OrderPayment.fromRawJson(String str) =>
      OrderPayment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderPayment.fromJson(Map<String, dynamic> json) => OrderPayment(
        info: json["info"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "info": info,
        "total": total,
      };
}

class PointsLog {
  PointsLog({
    this.couponRewardPoints,
    this.finalPoints,
    this.id,
    this.initPoints,
    this.maxRedeemPoints,
    this.promotionRewardPoints,
    this.purchaseRewardPoints,
    this.redeemPoints,
  });

  num? couponRewardPoints;
  num? finalPoints;
  num? id;
  num? initPoints;
  num? maxRedeemPoints;
  num? promotionRewardPoints;
  num? purchaseRewardPoints;
  num? redeemPoints;

  PointsLog copyWith({
    num? couponRewardPoints,
    num? finalPoints,
    num? id,
    num? initPoints,
    num? maxRedeemPoints,
    num? promotionRewardPoints,
    num? purchaseRewardPoints,
    num? redeemPoints,
  }) =>
      PointsLog(
        couponRewardPoints: couponRewardPoints ?? this.couponRewardPoints,
        finalPoints: finalPoints ?? this.finalPoints,
        id: id ?? this.id,
        initPoints: initPoints ?? this.initPoints,
        maxRedeemPoints: maxRedeemPoints ?? this.maxRedeemPoints,
        promotionRewardPoints:
            promotionRewardPoints ?? this.promotionRewardPoints,
        purchaseRewardPoints: purchaseRewardPoints ?? this.purchaseRewardPoints,
        redeemPoints: redeemPoints ?? this.redeemPoints,
      );

  factory PointsLog.fromRawJson(String str) =>
      PointsLog.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PointsLog.fromJson(Map<String, dynamic> json) => PointsLog(
        couponRewardPoints: json["coupon_reward_points"],
        finalPoints: json["final_points"],
        id: json["id"],
        initPoints: json["init_points"],
        maxRedeemPoints: json["max_redeem_points"],
        promotionRewardPoints: json["promotion_reward_points"],
        purchaseRewardPoints: json["purchase_reward_points"],
        redeemPoints: json["redeem_points"],
      );

  Map<String, dynamic> toJson() => {
        "coupon_reward_points": couponRewardPoints,
        "final_points": finalPoints,
        "id": id,
        "init_points": initPoints,
        "max_redeem_points": maxRedeemPoints,
        "promotion_reward_points": promotionRewardPoints,
        "purchase_reward_points": purchaseRewardPoints,
        "redeem_points": redeemPoints,
      };
}

class PromotionCoupon {
  PromotionCoupon({
    this.couponImageUrl,
    this.couponName,
    this.expiryDate,
  });

  String? couponImageUrl;
  String? couponName;
  String? expiryDate;

  PromotionCoupon copyWith({
    String? couponImageUrl,
    String? couponName,
    String? expiryDate,
  }) =>
      PromotionCoupon(
        couponImageUrl: couponImageUrl ?? this.couponImageUrl,
        couponName: couponName ?? this.couponName,
        expiryDate: expiryDate ?? this.expiryDate,
      );

  factory PromotionCoupon.fromRawJson(String str) =>
      PromotionCoupon.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionCoupon.fromJson(Map<String, dynamic> json) =>
      PromotionCoupon(
        couponImageUrl: json["coupon_image_url"],
        couponName: json["coupon_name"],
        expiryDate: json["expiry_date"],
      );

  Map<String, dynamic> toJson() => {
        "coupon_image_url": couponImageUrl,
        "coupon_name": couponName,
        "expiry_date": expiryDate,
      };
}
