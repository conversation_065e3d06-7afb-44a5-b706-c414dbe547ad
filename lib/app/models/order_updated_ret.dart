// To parse this JSON data, do
//
//     final orderUpdatedRet = orderUpdatedRetFromJson(jsonString);

import 'dart:convert';

class OrderUpdatedRet {
  OrderUpdatedRet({
    this.isUpdate,
    this.orderId,
  });

  bool? isUpdate;
  num? orderId;

  OrderUpdatedRet copyWith({
    bool? isUpdate,
    num? orderId,
  }) =>
      OrderUpdatedRet(
        isUpdate: isUpdate ?? this.isUpdate,
        orderId: orderId ?? this.orderId,
      );

  factory OrderUpdatedRet.fromRawJson(String str) =>
      OrderUpdatedRet.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderUpdatedRet.fromJson(Map<String, dynamic> json) =>
      OrderUpdatedRet(
        isUpdate: json["is_update"],
        orderId: json["order_id"],
      );

  Map<String, dynamic> toJson() => {
        "is_update": isUpdate,
        "order_id": orderId,
      };
}
