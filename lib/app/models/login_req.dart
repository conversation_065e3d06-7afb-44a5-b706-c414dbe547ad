// To parse this JSON data, do
//
//     final loginReq = loginReq<PERSON>rom<PERSON><PERSON>(jsonString);

import 'dart:convert';

class LoginReq {
  LoginReq({
    this.clientCode,
    this.channelCode,
    this.username,
    this.password,
  });

  String? clientCode;
  String? channelCode;
  String? username;
  String? password;

  LoginReq copyWith({
    String? clientCode,
    String? channelCode,
    String? username,
    String? password,
  }) =>
      LoginReq(
        clientCode: clientCode ?? this.clientCode,
        channelCode: channelCode ?? this.channelCode,
        username: username ?? this.username,
        password: password ?? this.password,
      );

  factory LoginReq.fromRawJson(String str) =>
      LoginReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginReq.fromJson(Map<String, dynamic> json) => LoginReq(
        clientCode: json["client_code"],
        channelCode: json["channel_code"],
        username: json["username"],
        password: json["password"],
      );

  Map<String, dynamic> toJson() => {
        "client_code": clientCode,
        "channel_code": channelCode,
        "username": username,
        "password": password,
      };
}
