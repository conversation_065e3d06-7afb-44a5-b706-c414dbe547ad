// To parse this JSON data, do
//
//     final orderInvoiceReq = orderInvoiceReqFromJson(jsonString);

import 'dart:convert';

class OrderInvoiceReq {
  OrderInvoiceReq({
    this.invoiceNumber,
    this.randomNumber,
    this.invoicePaper,
    this.vatNumber,
    this.carrierType,
    this.carrierId,
    this.npoBan,
  });

  String? invoiceNumber;
  String? randomNumber;
  bool? invoicePaper;
  String? vatNumber;
  num? carrierType;
  String? carrierId;
  String? npoBan;

  OrderInvoiceReq copyWith({
    String? invoiceNumber,
    String? randomNumber,
    bool? invoicePaper,
    String? vatNumber,
    num? carrierType,
    String? carrierId,
    String? npoBan,
  }) =>
      OrderInvoiceReq(
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        randomNumber: randomNumber ?? this.randomNumber,
        invoicePaper: invoicePaper ?? this.invoicePaper,
        vatNumber: vatNumber ?? this.vatNumber,
        carrierType: carrierType ?? this.carrierType,
        carrierId: carrierId ?? this.carrierId,
        npoBan: npoBan ?? this.npoBan,
      );

  factory OrderInvoiceReq.fromRawJson(String str) =>
      OrderInvoiceReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderInvoiceReq.fromJson(Map<String, dynamic> json) =>
      OrderInvoiceReq(
        invoiceNumber: json["invoice_number"],
        randomNumber: json["random_number"],
        invoicePaper: json["invoice_paper"],
        vatNumber: json["vat_number"],
        carrierType: json["carrier_type"],
        carrierId: json["carrier_id"],
        npoBan: json["npo_ban"],
      );

  Map<String, dynamic> toJson() => {
        "invoice_number": invoiceNumber,
        "random_number": randomNumber,
        "invoice_paper": invoicePaper,
        "vat_number": vatNumber,
        "carrier_type": carrierType,
        "carrier_id": carrierId,
        "npo_ban": npoBan,
      };
}
