// To parse this JSON data, do
//
//     final invoiceStatusReq = invoiceStatusReqFromJson(jsonString);

import 'dart:convert';

class InvoiceStatusReq {
  InvoiceStatusReq({
    this.invoiceNo,
    this.invoiceDate,
  });

  String? invoiceNo;
  String? invoiceDate;

  InvoiceStatusReq copyWith({
    String? invoiceNo,
    String? invoiceDate,
  }) =>
      InvoiceStatusReq(
        invoiceNo: invoiceNo ?? this.invoiceNo,
        invoiceDate: invoiceDate ?? this.invoiceDate,
      );

  factory InvoiceStatusReq.fromRawJson(String str) =>
      InvoiceStatusReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InvoiceStatusReq.fromJson(Map<String, dynamic> json) =>
      InvoiceStatusReq(
        invoiceNo: json["InvoiceNo"],
        invoiceDate: json["InvoiceDate"],
      );

  Map<String, dynamic> toJson() => {
        "InvoiceNo": invoiceNo,
        "InvoiceDate": invoiceDate,
      };
}
