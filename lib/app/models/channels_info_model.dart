// To parse this JSON data, do
//
//     final channelsInfo = channelsInfoFromJson(jsonString);

import 'dart:convert';

class ChannelsInfo {
  ChannelsInfo({
    this.address,
    this.brandId,
    this.businessHours,
    this.cashRatio,
    this.city,
    this.cityarea,
    this.clientId,
    this.code,
    this.createdAt,
    this.expiryDate,
    this.hasUsageLimit,
    this.id,
    this.industry,
    this.isRewardPoints,
    this.maxRedeemPrice,
    this.minPrice,
    this.name,
    this.other,
    this.redeemRatio,
    this.restrictedType,
    this.updatedAt,
    this.usagePeriod,
  });

  String? address;
  num? brandId;
  String? businessHours;
  num? cashRatio;
  City? city;
  City? cityarea;
  num? clientId;
  String? code;
  String? createdAt;
  String? expiryDate;
  bool? hasUsageLimit;
  num? id;
  num? industry;
  bool? isRewardPoints;
  num? maxRedeemPrice;
  num? minPrice;
  String? name;
  String? other;
  num? redeemRatio;
  num? restrictedType;
  String? updatedAt;
  num? usagePeriod;

  ChannelsInfo copyWith({
    String? address,
    num? brandId,
    String? businessHours,
    num? cashRatio,
    City? city,
    City? cityarea,
    num? clientId,
    String? code,
    String? createdAt,
    String? expiryDate,
    bool? hasUsageLimit,
    num? id,
    num? industry,
    bool? isRewardPoints,
    num? maxRedeemPrice,
    num? minPrice,
    String? name,
    String? other,
    num? redeemRatio,
    num? restrictedType,
    String? updatedAt,
    num? usagePeriod,
  }) =>
      ChannelsInfo(
        address: address ?? this.address,
        brandId: brandId ?? this.brandId,
        businessHours: businessHours ?? this.businessHours,
        cashRatio: cashRatio ?? this.cashRatio,
        city: city ?? this.city,
        cityarea: cityarea ?? this.cityarea,
        clientId: clientId ?? this.clientId,
        code: code ?? this.code,
        createdAt: createdAt ?? this.createdAt,
        expiryDate: expiryDate ?? this.expiryDate,
        hasUsageLimit: hasUsageLimit ?? this.hasUsageLimit,
        id: id ?? this.id,
        industry: industry ?? this.industry,
        isRewardPoints: isRewardPoints ?? this.isRewardPoints,
        maxRedeemPrice: maxRedeemPrice ?? this.maxRedeemPrice,
        minPrice: minPrice ?? this.minPrice,
        name: name ?? this.name,
        other: other ?? this.other,
        redeemRatio: redeemRatio ?? this.redeemRatio,
        restrictedType: restrictedType ?? this.restrictedType,
        updatedAt: updatedAt ?? this.updatedAt,
        usagePeriod: usagePeriod ?? this.usagePeriod,
      );

  factory ChannelsInfo.fromRawJson(String str) =>
      ChannelsInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ChannelsInfo.fromJson(Map<String, dynamic> json) => ChannelsInfo(
        address: json["address"] == null ? null : json["address"],
        brandId: json["brand_id"] == null ? null : json["brand_id"],
        businessHours:
            json["business_hours"] == null ? null : json["business_hours"],
        cashRatio: json["cash_ratio"] == null ? null : json["cash_ratio"],
        city: json["city"] == null ? null : City.fromJson(json["city"]),
        cityarea:
            json["cityarea"] == null ? null : City.fromJson(json["cityarea"]),
        clientId: json["client_id"] == null ? null : json["client_id"],
        code: json["code"] == null ? null : json["code"],
        createdAt: json["created_at"] == null ? null : json["created_at"],
        expiryDate: json["expiry_date"] == null ? null : json["expiry_date"],
        hasUsageLimit:
            json["has_usage_limit"] == null ? null : json["has_usage_limit"],
        id: json["id"] == null ? null : json["id"],
        industry: json["industry"] == null ? null : json["industry"],
        isRewardPoints:
            json["is_reward_points"] == null ? null : json["is_reward_points"],
        maxRedeemPrice:
            json["max_redeem_price"] == null ? null : json["max_redeem_price"],
        minPrice: json["min_price"] == null ? null : json["min_price"],
        name: json["name"] == null ? null : json["name"],
        other: json["other"] == null ? null : json["other"],
        redeemRatio: json["redeem_ratio"] == null ? null : json["redeem_ratio"],
        restrictedType:
            json["restricted_type"] == null ? null : json["restricted_type"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
        usagePeriod: json["usage_period"] == null ? null : json["usage_period"],
      );

  Map<String, dynamic> toJson() => {
        "address": address == null ? null : address,
        "brand_id": brandId == null ? null : brandId,
        "business_hours": businessHours == null ? null : businessHours,
        "cash_ratio": cashRatio == null ? null : cashRatio,
        "city": city?.toJson(),
        "cityarea": cityarea?.toJson(),
        "client_id": clientId == null ? null : clientId,
        "code": code == null ? null : code,
        "created_at": createdAt == null ? null : createdAt,
        "expiry_date": expiryDate == null ? null : expiryDate,
        "has_usage_limit": hasUsageLimit == null ? null : hasUsageLimit,
        "id": id == null ? null : id,
        "industry": industry == null ? null : industry,
        "is_reward_points": isRewardPoints == null ? null : isRewardPoints,
        "max_redeem_price": maxRedeemPrice == null ? null : maxRedeemPrice,
        "min_price": minPrice == null ? null : minPrice,
        "name": name == null ? null : name,
        "other": other == null ? null : other,
        "redeem_ratio": redeemRatio == null ? null : redeemRatio,
        "restricted_type": restrictedType == null ? null : restrictedType,
        "updated_at": updatedAt == null ? null : updatedAt,
        "usage_period": usagePeriod == null ? null : usagePeriod,
      };
}

class City {
  City({
    this.createdAt,
    this.id,
    this.name,
    this.updatedAt,
    this.cityId,
  });

  String? createdAt;
  num? id;
  String? name;
  String? updatedAt;
  num? cityId;

  City copyWith({
    String? createdAt,
    num? id,
    String? name,
    String? updatedAt,
    num? cityId,
  }) =>
      City(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
        cityId: cityId ?? this.cityId,
      );

  factory City.fromRawJson(String str) => City.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory City.fromJson(Map<String, dynamic> json) => City(
        createdAt: json["created_at"] == null ? null : json["created_at"],
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
        cityId: json["city_id"] == null ? null : json["city_id"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt == null ? null : createdAt,
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "updated_at": updatedAt == null ? null : updatedAt,
        "city_id": cityId == null ? null : cityId,
      };
}
