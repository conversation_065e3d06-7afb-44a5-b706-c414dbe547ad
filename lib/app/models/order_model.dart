// To parse this JSON data, do
//
//     final order = orderFromJson(jsonString);

import 'dart:convert';

class Order {
  Order({
    this.createdAt,
    this.id,
    this.memberName,
    this.orderNumber,
    this.paymentStatus,
    this.status,
    this.storeAccountName,
    this.total,
  });

  String? createdAt;
  num? id;
  String? memberName;
  String? orderNumber;
  num? paymentStatus;
  num? status;
  String? storeAccountName;
  num? total;

  Order copyWith({
    String? createdAt,
    num? id,
    String? memberName,
    String? orderNumber,
    num? paymentStatus,
    num? status,
    String? storeAccountName,
    num? total,
  }) =>
      Order(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        memberName: memberName ?? this.memberName,
        orderNumber: orderNumber ?? this.orderNumber,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        status: status ?? this.status,
        storeAccountName: storeAccountName ?? this.storeAccountName,
        total: total ?? this.total,
      );

  factory Order.fromRawJson(String str) => Order.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        createdAt: json["created_at"],
        id: json["id"],
        memberName: json["member_name"],
        orderNumber: json["order_number"],
        paymentStatus: json["payment_status"],
        status: json["status"],
        storeAccountName: json["store_account_name"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt,
        "id": id,
        "member_name": memberName,
        "order_number": orderNumber,
        "payment_status": paymentStatus,
        "status": status,
        "store_account_name": storeAccountName,
        "total": total,
      };
}
