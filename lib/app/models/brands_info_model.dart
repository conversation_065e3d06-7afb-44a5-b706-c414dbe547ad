// To parse this JSON data, do
//
//     final brandsInfo = brandsInfoFromJson(jsonString);

import 'dart:convert';

class BrandsInfo {
  BrandsInfo({
    this.address,
    this.bannerId,
    this.businessHours,
    this.city,
    this.cityarea,
    this.clientId,
    this.code,
    this.createdAt,
    this.domain,
    this.id,
    this.lineChannelId,
    this.lineLiffId,
    this.lineName,
    this.lineSecretCode,
    this.logoId,
    this.name,
    this.other,
    this.phone,
    this.pointRatio,
    this.status,
    this.taxId,
    this.timezone,
    this.updatedAt,
  });

  String? address;
  num? bannerId;
  String? businessHours;
  City? city;
  City? cityarea;
  num? clientId;
  String? code;
  String? createdAt;
  String? domain;
  num? id;
  String? lineChannelId;
  String? lineLiffId;
  String? lineName;
  String? lineSecretCode;
  num? logoId;
  String? name;
  String? other;
  String? phone;
  num? pointRatio;
  num? status;
  String? taxId;
  String? timezone;
  String? updatedAt;

  BrandsInfo copyWith({
    String? address,
    num? bannerId,
    String? businessHours,
    City? city,
    City? cityarea,
    num? clientId,
    String? code,
    String? createdAt,
    String? domain,
    num? id,
    String? lineChannelId,
    String? lineLiffId,
    String? lineName,
    String? lineSecretCode,
    num? logoId,
    String? name,
    String? other,
    String? phone,
    num? pointRatio,
    num? status,
    String? taxId,
    String? timezone,
    String? updatedAt,
  }) =>
      BrandsInfo(
        address: address ?? this.address,
        bannerId: bannerId ?? this.bannerId,
        businessHours: businessHours ?? this.businessHours,
        city: city ?? this.city,
        cityarea: cityarea ?? this.cityarea,
        clientId: clientId ?? this.clientId,
        code: code ?? this.code,
        createdAt: createdAt ?? this.createdAt,
        domain: domain ?? this.domain,
        id: id ?? this.id,
        lineChannelId: lineChannelId ?? this.lineChannelId,
        lineLiffId: lineLiffId ?? this.lineLiffId,
        lineName: lineName ?? this.lineName,
        lineSecretCode: lineSecretCode ?? this.lineSecretCode,
        logoId: logoId ?? this.logoId,
        name: name ?? this.name,
        other: other ?? this.other,
        phone: phone ?? this.phone,
        pointRatio: pointRatio ?? this.pointRatio,
        status: status ?? this.status,
        taxId: taxId ?? this.taxId,
        timezone: timezone ?? this.timezone,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory BrandsInfo.fromRawJson(String str) =>
      BrandsInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsInfo.fromJson(Map<String, dynamic> json) => BrandsInfo(
        address: json["address"],
        bannerId: json["banner_id"],
        businessHours: json["business_hours"],
        city: json["city"] == null ? null : City.fromJson(json["city"]),
        cityarea:
            json["cityarea"] == null ? null : City.fromJson(json["cityarea"]),
        clientId: json["client_id"],
        code: json["code"],
        createdAt: json["created_at"],
        domain: json["domain"],
        id: json["id"],
        lineChannelId: json["line_channel_id"],
        lineLiffId: json["line_liff_id"],
        lineName: json["line_name"],
        lineSecretCode: json["line_secret_code"],
        logoId: json["logo_id"],
        name: json["name"],
        other: json["other"],
        phone: json["phone"],
        pointRatio: json["point_ratio"],
        status: json["status"],
        taxId: json["tax_id"],
        timezone: json["timezone"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "address": address,
        "banner_id": bannerId,
        "business_hours": businessHours,
        "city": city?.toJson(),
        "cityarea": cityarea?.toJson(),
        "client_id": clientId,
        "code": code,
        "created_at": createdAt,
        "domain": domain,
        "id": id,
        "line_channel_id": lineChannelId,
        "line_liff_id": lineLiffId,
        "line_name": lineName,
        "line_secret_code": lineSecretCode,
        "logo_id": logoId,
        "name": name,
        "other": other,
        "phone": phone,
        "point_ratio": pointRatio,
        "status": status,
        "tax_id": taxId,
        "timezone": timezone,
        "updated_at": updatedAt,
      };
}

class City {
  City({
    this.createdAt,
    this.id,
    this.name,
    this.updatedAt,
    this.cityId,
  });

  String? createdAt;
  num? id;
  String? name;
  String? updatedAt;
  num? cityId;

  City copyWith({
    String? createdAt,
    num? id,
    String? name,
    String? updatedAt,
    num? cityId,
  }) =>
      City(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
        cityId: cityId ?? this.cityId,
      );

  factory City.fromRawJson(String str) => City.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory City.fromJson(Map<String, dynamic> json) => City(
        createdAt: json["created_at"],
        id: json["id"],
        name: json["name"],
        updatedAt: json["updated_at"],
        cityId: json["city_id"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt,
        "id": id,
        "name": name,
        "updated_at": updatedAt,
        "city_id": cityId,
      };
}
