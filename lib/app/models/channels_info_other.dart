// To parse this JSON data, do
//
//     final channelsInfoOther = channelsInfoOtherFromJson(jsonString);

import 'dart:convert';

class ChannelsInfoOther {
  ChannelsInfoOther({
    this.googleMap,
    this.website,
    this.shoppingWebsite,
    this.fb,
    this.ig,
    this.line,
    this.google,
    this.taxId,
  });

  GoogleMap? googleMap;
  String? website;
  String? shoppingWebsite;
  String? fb;
  String? ig;
  String? line;
  String? google;
  String? taxId;

  ChannelsInfoOther copyWith({
    GoogleMap? googleMap,
    String? website,
    String? shoppingWebsite,
    String? fb,
    String? ig,
    String? line,
    String? google,
    String? taxId,
  }) =>
      ChannelsInfoOther(
        googleMap: googleMap ?? this.googleMap,
        website: website ?? this.website,
        shoppingWebsite: shoppingWebsite ?? this.shoppingWebsite,
        fb: fb ?? this.fb,
        ig: ig ?? this.ig,
        line: line ?? this.line,
        google: google ?? this.google,
        taxId: taxId ?? this.taxId,
      );

  factory ChannelsInfoOther.fromRawJson(String str) =>
      ChannelsInfoOther.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ChannelsInfoOther.fromJson(Map<String, dynamic> json) =>
      ChannelsInfoOther(
        googleMap: json["google-map"] == null
            ? null
            : GoogleMap.fromJson(json["google-map"]),
        website: json["website"] == null ? null : json["website"],
        shoppingWebsite:
            json["shopping_website"] == null ? null : json["shopping_website"],
        fb: json["fb"] == null ? null : json["fb"],
        ig: json["ig"] == null ? null : json["ig"],
        line: json["line"] == null ? null : json["line"],
        google: json["google"] == null ? null : json["google"],
        taxId: json["tax_id"] == null ? null : json["tax_id"],
      );

  Map<String, dynamic> toJson() => {
        "google-map": googleMap?.toJson(),
        "website": website == null ? null : website,
        "shopping_website": shoppingWebsite == null ? null : shoppingWebsite,
        "fb": fb == null ? null : fb,
        "ig": ig == null ? null : ig,
        "line": line == null ? null : line,
        "google": google == null ? null : google,
        "tax_id": taxId == null ? null : taxId,
      };
}

class GoogleMap {
  GoogleMap({
    this.x,
    this.y,
  });

  String? x;
  String? y;

  GoogleMap copyWith({
    String? x,
    String? y,
  }) =>
      GoogleMap(
        x: x ?? this.x,
        y: y ?? this.y,
      );

  factory GoogleMap.fromRawJson(String str) =>
      GoogleMap.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GoogleMap.fromJson(Map<String, dynamic> json) => GoogleMap(
        x: json["x"] == null ? null : json["x"],
        y: json["y"] == null ? null : json["y"],
      );

  Map<String, dynamic> toJson() => {
        "x": x == null ? null : x,
        "y": y == null ? null : y,
      };
}
