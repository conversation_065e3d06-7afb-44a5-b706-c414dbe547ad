// To parse this JSON data, do
//
//     final logout = logoutFromJson(jsonString);

import 'dart:convert';

class Logout {
  Logout({
    this.isLogout,
  });

  bool? isLogout;

  Logout copyWith({
    bool? isLogout,
  }) =>
      Logout(
        isLogout: isLogout ?? this.isLogout,
      );

  factory Logout.fromRawJson(String str) => Logout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Logout.fromJson(Map<String, dynamic> json) => Logout(
        isLogout: json["is_logout"],
      );

  Map<String, dynamic> toJson() => {
        "is_logout": isLogout,
      };
}
