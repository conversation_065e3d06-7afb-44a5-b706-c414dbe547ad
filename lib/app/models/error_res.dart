// To parse this JSON data, do
//
//     final errorRes = errorResFromJson(jsonString);

import 'dart:convert';

class ErrorRes {
  ErrorRes({
    this.code,
    this.httpCode,
    this.message,
    this.request,
  });

  String? code;
  num? httpCode;
  String? message;
  String? request;

  ErrorRes copyWith({
    String? code,
    num? httpCode,
    String? message,
    String? request,
  }) =>
      ErrorRes(
        code: code ?? this.code,
        httpCode: httpCode ?? this.httpCode,
        message: message ?? this.message,
        request: request ?? this.request,
      );

  factory ErrorRes.fromRawJson(String str) =>
      ErrorRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ErrorRes.fromJson(Map<String, dynamic> json) => ErrorRes(
        code: json["code"],
        httpCode: json["http_code"],
        message: json["message"],
        request: json["request"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "http_code": httpCode,
        "message": message,
        "request": request,
      };
}
