import 'dart:async';
import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData;
import 'package:guests/app/models/error_res.dart';
import 'package:guests/app/models/login_req.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/brands_info_model.dart';
import 'package:guests/app/models/channels_info_model.dart';
import 'package:guests/app/models/error_message_model.dart';
import 'package:guests/app/models/login_res.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/app/models/client_info_model.dart';
import 'package:guests/app/models/logout_model.dart';
import 'package:guests/app/models/renew_token_model.dart';

import '../../keys.dart';
import 'box_provider.dart';
import 'pref_provider.dart';

class ApiProvider extends GetxService {
  static const _UNAUTHORIZED = 401;
  final Dio dio;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  final PrefProvider prefProvider;
  final String authority;
  final _disposable = Completer();
  final _statusCode = 0.obs;

  Dio get httpClient {
    final token = prefProvider.token;
    dio.options.contentType = Headers.formUrlEncodedContentType;
    dio.options.headers['Authorization'] = 'Bearer $token';
    dio.options.headers['Accept'] = 'application/json';
    return dio;
  }

  ApiProvider({
    required this.dio,
    required this.prefProvider,
  }) : authority = prefProvider.host;

  bool _onStatusCode(int? status) {
    _statusCode.value = status ?? 0;
    return status != null && status >= 200 && status < 300;
  }

  void _initObservable() {
    // status code 為未授權(401)，清空 token
    _statusCode.stream
        .debounceBuffer(1.seconds)
        .where((event) {
          if (event.length == 1) {
            return _UNAUTHORIZED == event.first;
          }
          if (event.length > 1) {
            return event.every((element) => _UNAUTHORIZED == element);
          }
          return false;
        })
        .takeUntil(_disposable.future)
        .listen((event) => prefProvider.token = '');
  }

  @override
  void onInit() {
    super.onInit();
    _initObservable();
    dio.options.validateStatus = _onStatusCode;
  }

  @override
  void onReady() {
    super.onReady();
    prefProvider.userDefault.put(kKeyToken, prefProvider.token);
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  ///
  /// 店長、店員登入
  ///
  Future<LoginRes> login(LoginReq data) async {
    try {
      final ret = await post(
        'login',
        data: data.toJson(),
        creator: (json) => LoginRes.fromJson(json),
      );
      // 特殊: 直接儲存 token
      prefProvider.token = ret.token;
      return ret;
    } on DioException catch (e) {
      if (e.response?.data?.containsKey(Keys.Error) == true) {
        final json = e.response!.data[Keys.Error];
        final err = ErrorRes.fromJson(json);
        if (Keys.Code0100 == err.code) {
          throw '帳號密碼或集團通路代號錯誤';
        }
      }
      rethrow;
    }
  }

  ///
  /// token 更新
  ///
  Future<String> renew() async {
    final ret = await getRaw<RenewToken>(
      unencodedPath: 'renew',
      creator: (json) => RenewToken.fromJson(json),
    );
    // 特殊: renew 完成的話直接記下 token
    final token = ret.token ?? '';
    prefProvider.token = token;
    return token;
  }

  ///
  /// 店長、店員登出
  ///
  Future<void> logout() async {
    try {
      final value = await getRaw(
        unencodedPath: 'logout',
        creator: (json) => Logout.fromJson(json),
      );
      if (value.isLogout == true) {
        kLogger.d('[ApiProvider] logout success');
      }
    } catch (error) {
      kLogger.e('[ApiProvider] logout error: $error');
    } finally {
      // 特殊: 登出的話直接清空 token
      prefProvider.token = '';
    }
  }

  ///
  /// 取得單一集團資料
  ///
  Future<ClientInfo> getClientInfo() {
    return getData(
      unencodedPath: 'clients/info',
      creator: (json) => ClientInfo.fromJson(json),
    );
  }

  ///
  /// 取得單一品牌資料
  ///
  Future<BrandsInfo> getBrandsInfo() {
    return getData(
      unencodedPath: 'brands/info',
      creator: (json) => BrandsInfo.fromJson(json),
    );
  }

  ///
  /// 取得單一通路資料
  ///
  Future<ChannelsInfo> getChannelsInfo() {
    return getData(
      unencodedPath: 'channels/info',
      creator: (json) => ChannelsInfo.fromJson(json),
    );
  }

  ///
  /// 取得 data 物件通用模板
  ///
  Future<T> getData<T>({
    required String unencodedPath,
    Map<String, dynamic>? filter,
    required T Function(dynamic json) creator,
  }) async {
    filter ??= <String, dynamic>{};
    filter.removeNull();
    final uri = Uri.https(authority, unencodedPath, filter.toStringMap());
    final res = await httpClient.getUri<Map>(uri);
    final data = res.data;
    if (data != null && data.containsKey(Keys.Error)) {
      final json = data[Keys.Error];
      throw ErrorMessage.fromJson(json);
    }
    if (data != null && data.containsKey(Keys.Data)) {
      final json = data[Keys.Data];
      return creator(json);
    }
    throw uri.path;
  }

  ///
  /// 取得 res 物件通用模板
  ///
  Future<T> getRaw<T>({
    required String unencodedPath,
    Map<String, dynamic>? filter,
    required T Function(dynamic json) creator,
  }) async {
    filter ??= <String, dynamic>{};
    filter.removeNull();
    final uri = Uri.https(authority, unencodedPath, filter.toStringMap());
    final res = await httpClient.getUri<Map>(uri);
    final data = res.data;
    if (data != null && data.containsKey(Keys.Error)) {
      final json = data[Keys.Error];
      throw ErrorMessage.fromJson(json);
    }
    if (data != null) {
      return creator(data);
    }
    throw uri.path;
  }

  ///
  /// post 通用模版
  ///
  Future<T> post<T>(
    String unencodedPath, {
    Map<String, dynamic>? data,
    required T Function(Map<String, dynamic> json) creator,
  }) async {
    try {
      final uri = Uri.https(authority, unencodedPath);
      data ??= <String, dynamic>{};
      data.removeNull();
      final res = await httpClient.postUri<Map<String, dynamic>>(
        uri,
        data: FormData.fromMap(data),
      );
      final responseData = res.data;
      if (responseData != null && responseData.containsKey(Keys.Error)) {
        final json = responseData[Keys.Error];
        throw ErrorMessage.fromJson(json);
      }
      if (responseData != null) {
        final ret = creator(responseData);
        return ret;
      }
      throw uri.path;
    } on DioException catch (e) {
      if (e.isSocketException) {
        throw '網路連線異常';
      }
      rethrow;
    }
  }

  ///
  /// put 通用模版
  ///
  Future<T> put<T>(
    String unencodedPath, {
    Map<String, dynamic>? data,
    required T Function(Map<String, dynamic> json) creator,
  }) async {
    final uri = Uri.https(authority, unencodedPath);
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.putUri<Map<String, dynamic>>(
      uri,
      data: FormData.fromMap(data),
    );
    final responseData = res.data;
    if (responseData != null && responseData.containsKey(Keys.Error)) {
      final json = responseData[Keys.Error];
      throw ErrorMessage.fromJson(json);
    }
    if (responseData != null) {
      final ret = creator(responseData);
      return ret;
    }
    throw uri.path;
  }
}
