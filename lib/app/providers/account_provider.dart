import 'dart:async';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:guests/app/models/reset_password_req.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/extension.dart';
import 'package:guests/keys.dart';
import 'package:guests/app/models/create_store_account_req.dart';
import 'package:guests/app/models/update_store_account_req.dart';

class Filter {
  String? keyword;

  bool validate(StoreAccount account) {
    if (keyword != null && keyword!.isNotEmpty) {
      if (!account.match(keyword!)) {
        return false;
      }
    }
    return true;
  }
}

class AccountProvider {
  final ApiProvider apiProvider;
  BoxProvider get boxProvider => apiProvider.boxProvider;
  GetStorage get storage => boxProvider.getGsBox(Keys.BoxAccount);

  AccountProvider({
    required this.apiProvider,
  });

  Stream<StoreAccount> getStoreAccounts([Filter? filter]) async* {
    final res = await apiProvider.getData(
      unencodedPath: 'store-accounts',
      creator: (json) => json,
      // creator: (json) => List.from(json).map((e) => StoreAccount.fromJson(e)),
    );
    final _storage = storage;
    _storage.erase();
    for (var json in Iterable.castFrom(res)) {
      final account = StoreAccount.fromJson(json);
      _storage.write('${account.id}', json);
      if (filter != null && !filter.validate(account)) {
        continue;
      }
      yield account;
    }
  }

  Iterable<StoreAccount> getStoreAccountsSync([Filter? filter]) sync* {
    final box = boxProvider.getGsBox(Keys.BoxAccount);
    for (var json in Iterable.castFrom(box.getValues())) {
      final account = StoreAccount.fromJson(json);
      if (filter != null && !filter.validate(account)) {
        continue;
      }
      yield account;
    }
  }

  Future<StoreAccount> getStoreAccount(String id) async {
    final res = await apiProvider.getData(
      unencodedPath: 'store-accounts/$id',
      creator: (json) => json,
    );
    if (res != null) {
      storage.write(id, res);
    }
    return StoreAccount.fromJson(res);
  }

  Future<num> createStoreAccount(CreateStoreAccountReq data) async {
    final res = await apiProvider.post<num>(
      'store-accounts',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey('account_id')) {
          final accountId = json['account_id'];
          return num.tryParse('$accountId') ?? 0;
        }
        return 0;
      },
    );
    if (res > 0) {
      getStoreAccount('$res');
    }
    return res;
  }

  Future<num> updateStoreAccount(String id, UpdateStoreAccountReq data) async {
    // 特殊处理，如果密码为空，则不更新密码
    if (data.password == '') {
      data.password = null;
    }
    final res = await apiProvider.put<num>(
      'store-accounts/$id',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey('account_id')) {
          final accountId = json['account_id'];
          return num.tryParse('$accountId') ?? 0;
        }
        return 0;
      },
    );
    if (res > 0) {
      getStoreAccount('$res');
    }
    return res;
  }

  ///
  /// 變更密碼
  ///
  Future<num> passwordReset(ResetPasswordReq data) async {
    try {
      return await apiProvider.post<num>(
        'profile/password-reset',
        data: data.toJson(),
        creator: (json) {
          if (json.containsKey('account_id')) {
            final accountId = json['account_id'];
            return num.tryParse('$accountId') ?? 0;
          }
          return 0;
        },
      );
    } on DioException catch (e) {
      if (e.response?.data?.containsKey(Keys.Error) == true) {
        final json = e.response!.data[Keys.Error];
        if (Keys.Code3101 == '${json[Keys.Code]}') {
          throw '舊密碼錯誤';
        }
      }
      rethrow;
    }
  }
}
