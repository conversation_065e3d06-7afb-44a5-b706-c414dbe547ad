import 'package:get/get.dart';
import 'package:guests/app/models/invoice_settings.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';

class InvoiceSettingsController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final _draft = InvoiceSettings().obs;
  InvoiceSettings get draft => _draft.value;

  InvoiceSettingsController({
    required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    try {
      draft.invoiceEnabled = prefProvider.invoiceEnabled;
      draft.invoiceSkipped = prefProvider.invoiceSkipped;
      draft.itemName = prefProvider.itemName;
      draft.taxType = prefProvider.taxType.toInt();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<void> submit() async {
    prefProvider.invoiceEnabled = draft.invoiceEnabled ?? false;
    prefProvider.invoiceSkipped = draft.invoiceSkipped ?? false;
    prefProvider.itemName = draft.itemName ?? '';
    prefProvider.taxType = draft.taxType ?? 1;
  }

  void refreshDraft() {
    _draft.refresh();
  }
}
