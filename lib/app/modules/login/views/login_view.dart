import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/custom_editor.dart';
import 'package:guests/app/components/dialog_custom.dart';

import 'package:guests/app/modules/login/controllers/login_controller.dart';
import 'package:guests/app/components/rounded_button.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/enums.dart';
import 'package:guests/ok_colors.dart';

class LoginView extends GetView<LoginController> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Background(
          background: SvgPicture.asset(
            'assets/images/background.svg',
            width: double.infinity,
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
          ),
          child: SafeArea(
            child: SizedBox.expand(
              child: Center(
                child: controller.obx(
                  (state) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                        vertical: Constants.paddingVertical,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: _children().toList(growable: false),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: Text(
          controller.packageInfo.displayVersion,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ).paddingSymmetric(
          vertical: 12.0,
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SvgPicture.asset(
      'assets/images/omos_logo.svg',
      width: 68.dw,
      height: 68.dh,
    );
    yield SizedBox(height: 8.dh);
    yield Text(
      controller.packageInfo.appName,
      style: const TextStyle(
        fontSize: 19,
        color: Colors.white,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    );
    yield SizedBox(height: 12.dh);
    yield _form();
  }

  Widget _form() {
    return Container(
      width: 300.dw,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(20.0)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _formChildren().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _formChildren() sync* {
    yield const SizedBox(height: 18.0);
    yield CustomEditor(
      initialValue: controller.draft.clientCode,
      labelColor: kColorPrimary,
      labelText: 'client_code'.tr,
      hintText: 'hint_client_code'.tr,
      onChanged: (value) => controller.draft.clientCode = value,
      validator: (value) {
        final clientCode = controller.draft.clientCode;
        if (clientCode == null || clientCode.isEmpty) {
          return 'msg_must_to_have'.tr;
        }
        return null;
      },
    );
    yield CustomEditor(
      initialValue: controller.draft.channelCode,
      labelColor: kColorPrimary,
      labelText: 'channel_code'.tr,
      hintText: 'hint_channel_code'.tr,
      onChanged: (value) => controller.draft.channelCode = value,
      validator: (value) {
        final channelCode = controller.draft.channelCode;
        if (channelCode == null || channelCode.isEmpty) {
          return 'msg_must_to_have'.tr;
        }
        return null;
      },
    );
    yield const SizedBox(height: 8.0);
    yield Obx(() {
      return TextButton.icon(
        onPressed: _checkPressed,
        icon: SizedBox.fromSize(
          size: const Size.square(20),
          child: Checkbox(
            value: controller.rememberMe,
            onChanged: (value) => _checkPressed(),
          ),
        ),
        label: Expanded(
          child: Text(
            '記住集團、通路及帳號',
            style: const TextStyle(
              fontSize: 14,
              color: OkColors.gray33,
            ),
            textAlign: TextAlign.left,
          ),
        ),
      ).paddingSymmetric(
        horizontal: 10.0,
      );
    });
    yield const SizedBox(height: 8.0);
    yield SvgPicture.asset(
      'assets/images/dot_line.svg',
      allowDrawingOutsideViewBox: true,
      fit: BoxFit.fitWidth,
    );
    yield CustomEditor(
      initialValue: controller.draft.username,
      labelText: 'username'.tr,
      hintText: 'hint_username'.tr,
      onChanged: (value) => controller.draft.username = value,
      validator: (value) {
        final username = controller.draft.username;
        if (username == null || username.isEmpty) {
          return 'msg_must_to_have'.tr;
        }
        return null;
      },
      style: const TextStyle(
        fontSize: 16,
        color: OkColors.gray66,
      ),
    );
    yield CustomEditor(
      obscureText: true,
      keyboardType: TextInputType.visiblePassword,
      initialValue: controller.draft.password,
      labelText: 'password'.tr,
      hintText: 'hint_password'.tr,
      onChanged: (value) => controller.draft.password = value,
      validator: (value) {
        final password = controller.draft.password;
        if (password == null || password.isEmpty) {
          return 'msg_must_to_have'.tr;
        }
        return null;
      },
      style: const TextStyle(
        fontSize: 16,
        color: OkColors.gray66,
      ),
    );
    yield const SizedBox(height: 16.0);
    yield RoundedButton(
      text: 'login'.tr,
      onPressed: _submit,
    ).paddingOnly(
      left: kPadding,
      right: kPadding,
      bottom: kPadding,
    );
  }

  Future<void> _checkPressed() async {
    try {
      await controller.onCheckBoxClicked();
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Future<void> _renewLogin() async {
    Get.showLoading();
    try {
      await controller.apiProvider.renew();
      Get.back();
      if (true == controller.rememberMe) {
        await controller.saveRememberMe();
      }
      Get.offAllNamed(Routes.SPLASH);
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  Future<void> _confirmLogin() async {
    final selection = await DialogCustom.showConfirm(
      titleText: '帳號已登入',
      contentText: '繼續登入會踢掉前一個登入者',
      rightButtonText: '繼續登入',
    );
    kLogger.d('[LoginView] 使用者選擇了: $selection');
    if (Button.Positive == selection) {
      await _renewLogin();
    } else {
      kLogger.d('[LoginView] 取消強制登入');
      // 取消強制登入，清空暫存 token
      controller.prefProvider.token = '';
    }
  }

  Future<void> _submit() async {
    if (_formKey.currentState?.validate() == false) {
      return;
    }
    Get.showLoading();
    try {
      final res = await controller.login();
      Get.back();
      if (true == res.alreadyLogin) {
        await _confirmLogin();
      } else {
        if (true == controller.rememberMe) {
          await controller.saveRememberMe();
        }
        await Get.offAllNamed(Routes.SPLASH);
      }
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }
}
