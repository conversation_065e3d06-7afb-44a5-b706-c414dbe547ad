import 'dart:async';

import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/login_req.dart';
import 'package:guests/app/models/login_res.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/constants.dart';
import 'package:hive/hive.dart';
import 'package:package_info_plus/package_info_plus.dart';

class LoginController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  PackageInfo get packageInfo => prefProvider.packageInfo;
  Box get userDefault => boxProvider.userDefault;

  final _rememberMe = false.obs;
  bool get rememberMe => _rememberMe.value;
  set rememberMe(bool value) => _rememberMe.value = value;

  final _draft = LoginReq().obs;
  LoginReq get draft => _draft.value;

  LoginController({
    required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _rememberMe.stream
        .debounce(500.milliseconds)
        .asyncMapSample((event) {
          kLogger.d('[LoginController] save rememberMe($event)');
          return userDefault.put(kKeyRememberMe, event);
        })
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      _rememberMe.value = userDefault.get(kKeyRememberMe, defaultValue: false);
    } catch (e) {
      kLogger.e('[LoginViewController] onRefresh: $e');
      _rememberMe.value = false;
    }
    if (rememberMe) {
      draft.clientCode = userDefault.get(kKeyClientCode, defaultValue: '');
      draft.channelCode = userDefault.get(kKeyChannelCode, defaultValue: '');
      draft.username = userDefault.get(kKeyUsername, defaultValue: '');
    }
    change('', status: RxStatus.success());
  }

  Future<LoginRes> login() async {
    return await apiProvider.login(draft);
  }

  Future<void> onCheckBoxClicked() async {
    final checked = rememberMe;
    kLogger.d('[LoginViewController] onCheckBoxClicked: checked($checked)');
    rememberMe = !checked;
    if (rememberMe == false) {
      await _resetRememberMe();
    }
  }

  Future<void> _resetRememberMe() async {
    kLogger.d('[LoginViewController] _resetRememberMe');
    await userDefault.put(kKeyClientCode, '');
    await userDefault.put(kKeyChannelCode, '');
    await userDefault.put(kKeyUsername, '');
  }

  Future<void> saveRememberMe() async {
    kLogger.d('[LoginViewController] _saveRememberMe');
    await userDefault.put(kKeyClientCode, draft.clientCode);
    await userDefault.put(kKeyChannelCode, draft.channelCode);
    await userDefault.put(kKeyUsername, draft.username);
  }
}
