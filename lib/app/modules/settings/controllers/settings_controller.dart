import 'package:get/get.dart';
import 'package:guests/app/providers/account_provider.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SettingsController extends GetxController with StateMixin<String> {
  final AccountProvider accountProvider;
  final PackageInfo packageInfo;
  ApiProvider get apiProvider => accountProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  SettingsController({
    required this.accountProvider,
    required this.packageInfo,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    change('', status: RxStatus.success());
  }
}
