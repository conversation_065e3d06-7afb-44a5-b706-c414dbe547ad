import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/primary_background.dart';
import 'package:guests/app/modules/settings/controllers/settings_controller.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/app/components/Avatar.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';

class SettingsView extends GetView<SettingsController> {
  SettingsView({
    Key? key,
  }) : super(key: key);

  void _onLogoutClicked() async {
    final button = await DialogCustom.showConfirm(
      titleText: '確認',
      contentText: '即將登出',
      rightButtonText: '登出',
    );
    if (button != null && button.isPositive) {
      controller.apiProvider.logout();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PrimaryBackground(
      child: Material(
        color: Colors.transparent,
        child: SafeArea(
          child: _body(),
        ),
      ),
    );
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield _header().sliverBox;
      yield SliverFillRemaining(
        hasScrollBody: false,
        fillOverscroll: true,
        child: DecoratedBox(
          decoration: const BoxDecoration(
            borderRadius: kTopRadius,
            color: kColorBackground,
          ),
          child: controller.obx(
            (state) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: _children().toList(growable: false),
            ),
          ),
        ),
      );
    }

    return CustomScrollView(
      slivers: children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 16.0,
        horizontal: kPadding,
      ),
      child: Avatar(
        data: controller.prefProvider.loginAccount,
        showStatus: false,
      ),
    );
    yield ColoredBox(
      color: Colors.white,
      child: _page(),
    );
    yield SizedBox(height: 36.0);
    yield Center(
      child: Text(
        // 'APP版本：V1.0.0',
        controller.packageInfo.displayVersion,
        style: const TextStyle(
          fontSize: 16,
          color: const Color(0xffb9b9b9),
        ),
        textAlign: TextAlign.center,
      ),
    );
    yield SizedBox(height: kBottomPadding);
  }

  Widget _header() {
    Iterable<Widget> children() sync* {
      yield SizedBox(
        width: double.infinity,
        height: 12.dh,
      );
      yield SvgPicture.asset(
        'assets/images/icon_store.svg',
        width: 90.dw,
        height: 90.dh,
      );
      yield SizedBox(
        height: 12.dh,
      );
      yield Text(
        // '賣東西零售店',
        controller.prefProvider.brandsInfo.name ?? '',
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w700,
          height: 1.1,
        ),
        textAlign: TextAlign.center,
      );
      yield SizedBox(height: 8.dh);
      yield Text(
        // '中山分店',
        controller.prefProvider.channelsInfo.name ?? '',
        style: TextStyle(
          fontSize: 14,
          color: Colors.white,
          height: 1.0,
        ),
        textAlign: TextAlign.center,
      );
      yield SizedBox(height: 10.dh);
    }

    return Background(
      background: Align(
        alignment: Alignment.topRight,
        child: TextButton(
          onPressed: _onLogoutClicked,
          child: Text(
            '登出',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _page() {
    Iterable<Widget> children() sync* {
      final role = controller.prefProvider.jwt.roleId.storeRole;
      // 修改密碼
      if ([StoreRole.Boss, StoreRole.Employee].contains(role)) {
        yield ListTile(
            onTap: () => Get.toNamed(Routes.RESET_PASSWORD),
            title: const Text('修改密碼'),
            trailing: const Icon(Icons.chevron_right_sharp));
      }
      // 操作員帳號設定
      if ([StoreRole.Boss].contains(role)) {
        yield ListTile(
          onTap: () => Get.toNamed(Routes.ACCOUNT_LIST),
          title: const Text('操作員帳號設定'),
          trailing: const Icon(Icons.chevron_right_sharp),
        );
      }
      // 電子發票設定
      if ([StoreRole.Boss, StoreRole.Employee].contains(role)) {
        yield ListTile(
          onTap: () => Get.toNamed(Routes.INVOICE_SETTINGS),
          title: const Text('電子發票設定'),
          trailing: const Icon(Icons.chevron_right_sharp),
        );
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }
}
