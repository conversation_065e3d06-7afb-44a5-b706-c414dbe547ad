import 'package:get/get.dart';
import 'package:guests/app/modules/home/<USER>/home_controller.dart';
import 'package:guests/app/modules/settings/controllers/settings_controller.dart';
import 'package:guests/app/modules/transactions/controllers/transactions_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HomeController>(
      () => HomeController(
        orderProvider: Get.find(),
        invoiceProvider: Get.find(),
        accountProvider: Get.find(),
      ),
    );
    Get.lazyPut<SettingsController>(
      () => SettingsController(
        accountProvider: Get.find(),
        packageInfo: Get.find(),
      ),
    );
    Get.lazyPut<TransactionsController>(
      () => TransactionsController(
        orderProvider: Get.find(),
      ),
    );
  }
}
