import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/custom_editor.dart';
import 'package:guests/app/modules/reset_password/controllers/reset_password_controller.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/ok_colors.dart';

class ResetPasswordView extends GetView<ResetPasswordController> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('密碼修改'),
      ),
      body: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: kTopRadius,
          color: OkColors.background,
        ),
        child: Background(
          background: Form(
            key: _formKey,
            child: _body(),
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: BottomButton('確定', onPressed: _submit),
          ),
        ),
      ),
    );
  }

  Widget _body() {
    return ListView(
      padding: EdgeInsets.only(
        bottom: kBottomPadding,
      ),
      children: _buildList().toList(growable: false),
    );
  }

  Iterable<Widget> _buildList() sync* {
    yield Text(
      '請填寫密碼修改資料',
      style: TextStyle(
        fontSize: 16,
        color: OkColors.primary,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ).paddingSymmetric(vertical: kDefaultPadding);
    yield CustomEditor(
      obscureText: true,
      labelText: '舊密碼', // TODO: i18n
      hintText: '請輸入目前密碼', // TODO: i18n
      onChanged: controller.currentPassword,
      validator: (value) {
        if (controller.currentPassword.value.isEmpty) {
          return '請輸入目前密碼';
        }
        return null;
      },
    );
    yield CustomEditor(
      obscureText: true,
      labelText: '新密碼', // TODO: i18n
      hintText: '請輸入新密碼', // TODO: i18n
      onChanged: this.controller.newPassword,
      validator: (value) {
        if (controller.newPassword.value.isEmpty) {
          return '請輸入新密碼';
        }
        return null;
      },
    );
    yield CustomEditor(
      obscureText: true,
      labelText: '再次輸入新密碼', // TODO: i18n
      hintText: '請輸入相同的密碼', // TODO: i18n
      onChanged: controller.checkPassword,
      validator: (value) {
        final x = controller.newPassword.value;
        final y = controller.checkPassword.value;
        if (x != y) {
          return '請輸入相同的新密碼';
        }
        return null;
      },
    );
  }

  Future<void> _submit() async {
    final state = _formKey.currentState;
    if (state != null && state.validate()) {
      Get.showLoading();
      try {
        await controller.submit();
        Get.back();
        Get.back();
      } catch (e) {
        Get.back();
        Get.showAlert(e.toString());
      }
    }
  }
}
