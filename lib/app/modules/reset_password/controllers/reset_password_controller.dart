import 'package:get/get.dart';
import 'package:guests/app/models/reset_password_req.dart';
import 'package:guests/app/providers/account_provider.dart';

class ResetPasswordController extends GetxController {
  final AccountProvider accountProvider;
  final currentPassword = ''.obs;
  final newPassword = ''.obs;
  final checkPassword = ''.obs;

  ResetPasswordController({
    required this.accountProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {}

  @override
  void onClose() {}

  Future<num> submit() {
    return accountProvider.passwordReset(
      ResetPasswordReq(
        oldPassword: currentPassword.value,
        newPassword: newPassword.value,
        checkPassword: checkPassword.value,
      ),
    );
  }
}
