import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/account_list_item.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/modules/account_list/controllers/account_list_controller.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/constants.dart';
import 'package:guests/ok_colors.dart';

class AccountListView extends GetView<AccountListController> {
  const AccountListView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('操作員帳號設定'),
      ),
      body: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: kTopRadius,
          color: OkColors.background,
        ),
        child: Background(
          background: _main(),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: BottomButton(
              '新增操作員',
              onPressed: () => Get.toNamed(Routes.ACCOUNT_DETAIL),
            ),
          ),
        ),
      ),
    );
  }

  Widget _list() {
    final it = controller.accounts;
    if (it.isEmpty) {
      return const Blank();
    }
    return ListView.separated(
      padding: const EdgeInsets.only(
        bottom: kBottomPadding,
      ),
      itemCount: it.length,
      separatorBuilder: (context, index) {
        return const SizedBox(
          height: 8.0,
        );
      },
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return AccountListItem(
          data: data,
          onPressed: () {
            Get.toNamed(
              Routes.ACCOUNT_DETAIL,
              parameters: {
                'id': '${data.id}',
              },
            );
          },
        );
      },
    );
  }

  Iterable<Widget> _children() sync* {
    yield _searchBar();
    yield Expanded(
      child: controller.obx(
        (value) => _list(),
        onEmpty: Blank(),
      ),
    );
  }

  Widget _searchBar() {
    return TextField(
      onChanged: (value) {
        controller.keyword = value;
      },
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        hintText: '請輸入操作員名稱或帳號',
        prefixIcon: const Icon(
          Icons.search,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
          borderRadius: kBorderRadius,
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            color: OkColors.primary,
          ),
          borderRadius: kBorderRadius,
        ),
      ),
    ).paddingSymmetric(
      vertical: 8.0,
      horizontal: 8.0,
    );
  }

  Widget _main() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }
}
