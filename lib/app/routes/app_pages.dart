import 'package:get/get.dart';

import '../modules/account_detail/bindings/account_detail_binding.dart';
import '../modules/account_detail/views/account_detail_view.dart';
import '../modules/account_list/bindings/account_list_binding.dart';
import '../modules/account_list/views/account_list_view.dart';
import '../modules/create_order/bindings/create_order_binding.dart';
import '../modules/create_order/views/create_order_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/invoice_settings/bindings/invoice_settings_binding.dart';
import '../modules/invoice_settings/views/invoice_settings_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/order_detail/bindings/order_detail_binding.dart';
import '../modules/order_detail/views/order_detail_view.dart';
import '../modules/reset_password/bindings/reset_password_binding.dart';
import '../modules/reset_password/views/reset_password_view.dart';
import '../modules/settings/bindings/settings_binding.dart';
import '../modules/settings/views/settings_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';
import 'auth_middleware.dart';

part 'app_routes.dart';

class AppPages {
  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: Routes.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
      middlewares: [
        AuthMiddleware(),
      ],
    ),
    GetPage(
      name: Routes.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: Routes.SETTINGS,
      page: () => SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: Routes.RESET_PASSWORD,
      page: () => ResetPasswordView(),
      binding: ResetPasswordBinding(),
    ),
    GetPage(
      name: Routes.ACCOUNT_LIST,
      page: () => AccountListView(),
      binding: AccountListBinding(),
    ),
    GetPage(
      name: Routes.ACCOUNT_DETAIL,
      page: () => AccountDetailView(),
      binding: AccountDetailBinding(),
    ),
    GetPage(
      name: Routes.INVOICE_SETTINGS,
      page: () => InvoiceSettingsView(),
      binding: InvoiceSettingsBinding(),
    ),
    GetPage(
      name: Routes.ORDER_DETAIL,
      page: () => OrderDetailView(),
      binding: OrderDetailBinding(),
    ),
    GetPage(
      name: Routes.CREATE_ORDER,
      page: () => CreateOrderView(),
      binding: CreateOrderBinding(),
      middlewares: [
        AuthMiddleware(),
      ],
    ),
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
  ];
}
