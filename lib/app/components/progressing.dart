import 'package:flutter/material.dart';
import 'package:guests/ok_colors.dart';

class Progressing extends StatelessWidget {
  final String? message;
  final Widget child;

  Progressing({
    Key? key,
    this.message,
  })  : child = SizedBox.fromSize(
          size: const Size.square(80.0),
          child: const CircularProgressIndicator(),
        ),
        super(key: key);

  const Progressing.check({
    Key? key,
    this.message,
  })  : child = const Icon(
          Icons.check_circle_outline,
          color: OkColors.primary,
          size: 120,
        ),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 128.0,
        height: 128.0,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: Colors.white,
        ),
        child: child,
      ),
    );
  }
}
