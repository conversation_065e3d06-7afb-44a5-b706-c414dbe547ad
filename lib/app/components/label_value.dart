import 'package:flutter/material.dart';

class LabelValue extends StatelessWidget {
  final String? labelText;
  final String? valueText;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  LabelValue({
    Key? key,
    this.labelText,
    this.valueText,
    this.labelStyle,
    this.valueStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        style: this.labelStyle ??
            TextStyle(
              fontSize: 16.0,
              color: Colors.black,
            ),
        children: [
          TextSpan(
            text: this.labelText ?? '',
            style: this.labelStyle ??
                const TextStyle(
                  fontSize: 16.0,
                  color: const Color(0xff666666),
                ),
          ),
          TextSpan(
            text: this.valueText ?? '',
            style: this.valueStyle ??
                const TextStyle(
                  fontSize: 16.0,
                  color: Colors.black,
                ),
          ),
        ],
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }
}
