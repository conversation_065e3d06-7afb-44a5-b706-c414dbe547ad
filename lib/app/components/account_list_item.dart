import 'package:flutter/material.dart';
import 'package:guests/app/components/Avatar.dart';
import 'package:guests/app/components/label_value.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';

/// 帳號列表項目組件
/// 
/// 用於顯示操作員帳號信息的可重用組件，包含：
/// - 用戶頭像和基本信息
/// - 上次登入時間
/// - 編輯按鈕
class AccountListItem extends StatelessWidget {
  /// 帳號數據
  final StoreAccount data;
  
  /// 點擊編輯按鈕的回調函數
  final VoidCallback onPressed;

  const AccountListItem({
    Key? key,
    required this.data,
    required this.onPressed,
  }) : super(key: key);

  /// 構建組件的子元素
  Iterable<Widget> _children() sync* {
    // 用戶頭像區域
    yield Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: kPadding,
        vertical: 16.0,
      ),
      child: Avatar(
        data: data,
      ),
    );
    
    // 分隔線
    yield const Divider(
      height: 1.0,
      indent: kPadding,
      endIndent: kPadding,
    );
    
    // 上次登入時間信息
    yield Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 12.0,
        horizontal: kPadding,
      ),
      child: LabelValue(
        labelText: '上次登入時間  ',
        labelStyle: const TextStyle(
          fontSize: 16,
          color: Colors.black,
          height: 2,
        ),
        valueText: data.displayLastLogin,
        valueStyle: const TextStyle(
          fontFamily: 'Helvetica Neue',
          fontSize: 16,
          color: Color(0xff3e4b5a),
          height: 2,
        ),
      ),
    );
    
    // 編輯按鈕
    yield Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: kPadding,
      ),
      child: TextButton.icon(
        onPressed: onPressed,
        icon: const Icon(
          Icons.edit,
          color: Color(0xFF3E4B5A),
        ),
        label: const Text(
          '編輯', // TODO: i18n
          style: TextStyle(
            fontSize: 14,
            color: Color(0xff3e4b5a),
          ),
          textAlign: TextAlign.left,
        ),
        style: ButtonStyle(
          side: MaterialStateProperty.all(const BorderSide(
            width: 1.0,
            color: Color(0xffdbdbea),
          )),
          padding: MaterialStateProperty.all(EdgeInsets.zero),
          backgroundColor: MaterialStateProperty.all(kColorBackground),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 30.0)),
        ),
      ),
    );
    
    // 底部間距
    yield const SizedBox(
      height: 12.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _children().toList(),
      ),
    );
  }
}
