import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

import 'rounded_button.dart';

class BottomButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String buttonText;

  const BottomButton(
    this.buttonText, {
    Key? key,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, -0.45),
          colors: [Colors.white.withOpacity(0.0), Colors.white],
          stops: [0.0, 1.0],
        ),
      ),
      child: SafeArea(
        minimum: EdgeInsets.all(kPadding),
        maintainBottomViewPadding: true,
        child: RoundedButton(
          text: buttonText,
          onPressed: onPressed,
        ),
      ),
    );
  }
}
