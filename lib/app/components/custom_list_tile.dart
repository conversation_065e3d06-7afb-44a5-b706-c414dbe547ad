import 'package:flutter/material.dart';
import 'package:guests/app/components/label_value.dart';

class CustomListTile extends StatelessWidget {
  final String? leadingText;
  final String? titleText;
  final String? trailText;
  final Color? backgroundColor;
  final EdgeInsets? padding;

  CustomListTile({
    Key? key,
    this.leadingText,
    this.titleText,
    this.trailText,
    this.padding,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: this.backgroundColor ?? Colors.white,
      padding: this.padding,
      child: Row(
        children: [
          Expanded(
            child: LabelValue(
              labelText: this.leadingText,
              valueText: this.titleText,
            ),
          ),
          Text(
            this.trailText ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
