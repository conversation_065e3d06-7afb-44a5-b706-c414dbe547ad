import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class CircleButton extends StatelessWidget {
  final double size;
  final VoidCallback? onPressed;
  final Widget? icon;
  final Widget? text;

  CircleButton({
    this.size = 76,
    this.onPressed,
    this.icon,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    // return MaterialButton(
    //   onPressed: this.onPressed,
    //   shape: CircleBorder(),
    //   padding: EdgeInsets.all(0.0),
    //   child: Ink(
    //     width: this.size,
    //     height: this.size,
    //     decoration: BoxDecoration(
    //       shape: BoxShape.circle,
    //       gradient: LinearGradient(
    //         begin: Alignment(0.0, -1.0),
    //         end: Alignment(0.0, 1.0),
    //         colors: [
    //           const Color(0xfff89321),
    //           const Color(0xffe66f53),
    //         ],
    //         stops: [0.0, 1.0],
    //       ),
    //       // boxShadow: [
    //       //   BoxShadow(
    //       //     color: const Color(0x40000000),
    //       //     offset: Offset(0, 0),
    //       //     blurRadius: 10,
    //       //   ),
    //       // ],
    //     ),
    //     child: Column(
    //       mainAxisSize: MainAxisSize.min,
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       crossAxisAlignment: CrossAxisAlignment.center,
    //       children: [
    //         this.icon,
    //         this.text,
    //       ],
    //     ),
    //   ),
    // );
    return Container(
      width: this.size,
      height: this.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, 1.0),
          colors: [
            kColorPrimary,
            kColorSecondary,
          ],
          stops: [0.0, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x40000000),
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: TextButton(
        style: TextButton.styleFrom(
          shape: CircleBorder(),
        ),
        onPressed: this.onPressed,
        child: Tab(
          iconMargin: EdgeInsets.zero,
          icon: this.icon,
          child: this.text,
        ),
      ),
    );
    // return SizedBox(
    //   width: this.size,
    //   height: this.size,
    //   child: FloatingActionButton(
    //     onPressed: this.onPressed,
    //     child: Ink(
    //       width: this.size,
    //       height: this.size,
    //       decoration: BoxDecoration(
    //         shape: BoxShape.circle,
    //         gradient: LinearGradient(
    //           begin: Alignment(0.0, -1.0),
    //           end: Alignment(0.0, 1.0),
    //           colors: [
    //             kPrimaryColor,
    //             kSecondaryColor,
    //           ],
    //           stops: [0.0, 1.0],
    //         ),
    //         // boxShadow: [
    //         //   BoxShadow(
    //         //     color: const Color(0x40000000),
    //         //     offset: Offset(0, 0),
    //         //     blurRadius: 10,
    //         //   ),
    //         // ],
    //       ),
    //       child: Column(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.center,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           this.icon,
    //           this.text,
    //         ],
    //       ),
    //     ),
    //   ),
    // );
  }
}
