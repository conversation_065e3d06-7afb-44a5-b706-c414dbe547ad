import 'package:barcode/barcode.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/models/invoice_model.dart';
import 'package:guests/extension.dart';

class InvoicePage extends StatelessWidget {
  static const _TITLE_FONT_SIZE = 32.0;
  static const _DEF_FONT_SIZE = 26.0;
  final InvoiceModel data;

  final _dm = Barcode.code39();
  final _qr = Barcode.qrCode(typeNumber: 6);

  InvoicePage(
    this.data, {
    Key? key,
    String? storeName,
    String? productName,
  }) : super(key: key) {
    if (storeName != null) data.storeName = storeName;
    if (productName != null) data.productName = productName;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      width: 372.0,
      child: SingleChildScrollView(
        physics: NeverScrollableScrollPhysics(),
        child: MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaleFactor: 1.0,
            boldText: false,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                data.storeName ?? '企業人企業識別標章',
                style: TextStyle(
                  fontSize: _TITLE_FONT_SIZE,
                  color: Colors.black,
                ),
                maxLines: 1,
                softWrap: false,
                textAlign: TextAlign.center,
              ),
              Text(
                this.data.displayPrintMark,
                maxLines: 1,
                softWrap: false,
                style: TextStyle(
                  fontSize: this.data.printMarkFontSize,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                data.displayTwDateTime,
                maxLines: 1,
                softWrap: false,
                style: const TextStyle(
                  fontSize: 40,
                  color: Colors.black,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                // 'XX-12345678',
                data.displayInvoiceNumber,
                maxLines: 1,
                softWrap: false,
                style: TextStyle(
                  fontSize: 40,
                  color: Colors.black,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      this.data.displayDateTime,
                      maxLines: 1,
                      softWrap: false,
                      style: TextStyle(
                        fontSize: _DEF_FONT_SIZE,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Visibility(
                    visible: data.hasBuyer,
                    child: Text(
                      '格式 25',
                      maxLines: 1,
                      softWrap: false,
                      style: TextStyle(
                        fontSize: _DEF_FONT_SIZE,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ).paddingSymmetric(
                vertical: 4.0,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 7,
                    child: Text(
                      this.data.displayRandomNumber,
                      maxLines: 1,
                      softWrap: false,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontSize: _DEF_FONT_SIZE,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 8,
                    child: Text(
                      this.data.displayTotalAmount,
                      style: TextStyle(
                        fontSize: _DEF_FONT_SIZE,
                        color: Colors.black,
                      ),
                      maxLines: 1,
                      softWrap: false,
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      data.displaySeller,
                      style: TextStyle(
                        fontSize: _DEF_FONT_SIZE,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.left,
                      maxLines: 1,
                      softWrap: false,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      data.displayBuyer,
                      style: TextStyle(
                        fontSize: _DEF_FONT_SIZE,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.right,
                      maxLines: 1,
                      softWrap: false,
                    ),
                  ),
                ],
              ),
              SizedBox(
                width: double.infinity,
                height: 60.0,
                // color: Colors.red,
                child: SvgPicture.string(
                  _dm.toSvg(
                    data.barcode,
                    width: 345.0,
                    height: 60.0,
                    drawText: false,
                    // fontFamily: 'arial_24',
                  ),
                ),
              ).paddingSymmetric(
                vertical: 4.0,
              ),
              Row(
                children: [
                  Expanded(
                    child: SvgPicture.string(
                      _qr.toSvg(
                        data.leftQrString,
                        height: 150.0,
                        width: 150.0,
                      ),
                      height: 150.0,
                    ),
                  ),
                  const SizedBox(
                    width: 16.0,
                  ),
                  Expanded(
                    child: SvgPicture.string(
                      _qr.toSvg(
                        this.data.rightQrString,
                        height: 150.0,
                        width: 150.0,
                      ),
                      height: 150.0,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
