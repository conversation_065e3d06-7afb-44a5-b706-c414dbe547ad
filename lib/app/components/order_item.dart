import 'package:flutter/material.dart';
import 'package:guests/app/models/order_model.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';

class OrderItem extends StatelessWidget {
  final Order data;
  final ValueChanged<Order>? onPressed;

  const OrderItem({
    Key? key,
    required this.data,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 12.0,
              ),
              Padding(
                padding: kContentPadding,
                child: Row(
                  children: [
                    TextButton(
                      style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all(kColorPrimary),
                        padding: MaterialStateProperty.all(EdgeInsets.symmetric(
                          horizontal: 2.0,
                          vertical: 2.0,
                        )),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        minimumSize: MaterialStateProperty.all(Size.zero),
                      ),
                      onPressed: onPressed != null ? () => onPressed!(data) : null,
                      child: Row(
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 8.0,
                          ),
                          Text(
                            '詳情',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Icon(
                            Icons.keyboard_arrow_right,
                            color: Colors.white,
                          ),
                          SizedBox(
                            width: 4.0,
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Text(
                      this.data.displayStatus,
                      style: TextStyle(
                        fontSize: 16,
                        color: this.data.displayStatusColor,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              ),
              _ListTile(
                labelText: '訂單編號：',
                valueText: this.data.orderNumber,
                trailText: this.data.displayDateTimeMdHm,
              ),
              // SizedBox(
              //   height: 8.0,
              // ),
              // _ListTile(
              //   labelText: '發票號碼：',
              // ),
              SizedBox(
                height: 12.0,
              ),
            ],
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xffEDEDED),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: kPadding,
            vertical: 12.0,
          ),
          alignment: Alignment.centerRight,
          child: Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 28,
                color: kColorPrimary,
              ),
              children: [
                TextSpan(
                  text: (this.data.total ?? 0).currency,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: ' 元',
                  style: TextStyle(
                    fontSize: 17,
                    color: const Color(0xff222222),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.left,
          ),
        ),
        Container(
          height: 22.0,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [
                const Color(0xff868686),
                const Color(0xffa0a0a0),
                const Color(0xffbcbcbc)
              ],
              stops: [0.0, 0.187, 1.0],
            ),
          ),
        ),
      ],
    );
  }
}

class _ListTile extends StatelessWidget {
  final double horizontalPadding;
  final double verticalPadding;
  final String labelText;
  final String? valueText;
  final String? trailText;

  _ListTile({
    Key? key,
    this.labelText = '',
    this.valueText = '',
    this.trailText = '',
    this.horizontalPadding = kPadding,
    this.verticalPadding = 0.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: this.horizontalPadding,
        vertical: this.verticalPadding,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text.rich(
              TextSpan(
                style: TextStyle(
                  fontSize: 16,
                  color: const Color(0xff6d7278),
                ),
                children: [
                  TextSpan(
                    text: this.labelText,
                  ),
                  TextSpan(
                    // text: 'line2021098765',
                    text: this.valueText ?? '',
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
          ),
          Text(
            this.trailText ?? '',
            style: TextStyle(
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}

// class TransectionItem2 extends StatelessWidget {
//   final Order data;
//   final Function onPressed;

//   const TransectionItem2({
//     Key key,
//     this.data,
//     this.onPressed,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: this.onPressed,
//       child: Container(
//         padding: EdgeInsets.symmetric(
//           horizontal: kPadding,
//           vertical: 8.0,
//         ),
//         color: Colors.white,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisSize: MainAxisSize.max,
//               children: [
//                 CachedNetworkImage(
//                   imageUrl: '',
//                   imageBuilder: (context, imageProvider) {
//                     return CircleAvatar(
//                       backgroundImage: imageProvider,
//                       radius: 18.0,
//                     );
//                   },
//                   placeholder: (context, url) {
//                     return SvgPicture.asset('assets/images/avatar.svg');
//                   },
//                 ),
//                 SizedBox(
//                   width: 12.0,
//                 ),
//                 Expanded(
//                   child: Text(
//                     this.data.memberName ?? '-',
//                     style: TextStyle(
//                       fontFamily: 'Helvetica Neue',
//                       fontSize: 16,
//                       color: const Color(0xff000000),
//                       fontWeight: FontWeight.w700,
//                     ),
//                     softWrap: false,
//                     overflow: TextOverflow.ellipsis,
//                     textAlign: TextAlign.left,
//                   ),
//                 ),
//                 Text(
//                   'store_account_name'.tr +
//                       (this.data?.storeAccountName ?? '-'),
//                   style: TextStyle(
//                     fontFamily: 'Helvetica Neue',
//                     fontSize: 14,
//                     color: const Color(0xfff89328),
//                   ),
//                   textAlign: TextAlign.left,
//                 ),
//                 SizedBox(
//                   width: 16.0,
//                 ),
//                 Text(
//                   this.data?.displayStatus ?? '-',
//                   style: TextStyle(
//                     fontFamily: 'Helvetica Neue',
//                     fontSize: 14,
//                     color: this.data.displayStatusColor,
//                   ),
//                   textAlign: TextAlign.right,
//                 ),
//               ],
//             ),
//             Divider(),
//             SizedBox(
//               height: 8.0,
//             ),
//             Text(
//               'order_number'.tr + (this.data?.orderNumber ?? '-'),
//               style: TextStyle(
//                 fontFamily: 'Helvetica Neue',
//                 fontSize: 14,
//                 color: const Color(0xff666666),
//               ),
//               textAlign: TextAlign.left,
//             ),
//             SizedBox(
//               height: 8.0,
//             ),
//             Row(
//               children: [
//                 Expanded(
//                   child: Text(
//                     'consume_on_site'.tr,
//                     style: TextStyle(
//                       fontFamily: 'Helvetica Neue',
//                       fontSize: 16,
//                       color: Colors.black,
//                       fontWeight: FontWeight.w700,
//                     ),
//                     textAlign: TextAlign.left,
//                     softWrap: false,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ),
//                 Text(
//                   '\$${this.data.displayTotal}',
//                   style: TextStyle(
//                     fontFamily: 'Helvetica Neue',
//                     fontSize: 20,
//                     color: Colors.black,
//                     fontWeight: FontWeight.w500,
//                   ),
//                   textAlign: TextAlign.right,
//                 ),
//               ],
//             ),
//             Divider(),
//             Container(
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(5.0),
//                 color: const Color(0xffeeeef3),
//               ),
//               padding: EdgeInsets.symmetric(
//                 vertical: 4.0,
//               ),
//               child: Row(
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.symmetric(horizontal: 8.0),
//                     child: Icon(
//                       Icons.schedule,
//                       color: const Color(0xff666666),
//                     ),
//                   ),
//                   Text(
//                     'consume_at'.tr + data.displayDateTime,
//                     style: TextStyle(
//                       fontFamily: 'Helvetica Neue',
//                       fontSize: 14,
//                       color: const Color(0xff666666),
//                     ),
//                     textAlign: TextAlign.left,
//                   ),
//                 ],
//               ),
//               // child: ListTile(
//               //   dense: true,
//               //   leading: Icon(Icons.schedule),
//               //   title: Text(
//               //     '消費時間：2020/11/05　13:15',
//               //     style: TextStyle(
//               //       fontFamily: 'Helvetica Neue',
//               //       fontSize: 14,
//               //       color: const Color(0xff666666),
//               //     ),
//               //     textAlign: TextAlign.left,
//               //   ),
//               // ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
