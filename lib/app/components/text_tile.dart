import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class TextTile extends StatelessWidget {
  final String text;
  final bool isNew;
  final VoidCallback? onPressed;
  final Color color;

  const TextTile(
    this.text, {
    this.onPressed,
    this.isNew = false,
    this.color = Colors.white,
    Key? key,
  }) : super(key: key);

  static Widget expand(
    String text, {
    VoidCallback? onPressed,
  }) {
    return ListTile(
      onTap: onPressed,
      title: Text(text),
      trailing: const Icon(Icons.expand_more),
    );
  }

  static Widget header(String text) {
    return ListTile(
      dense: true,
      title: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff666666),
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onPressed,
      title: Text(text),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            visible: isNew,
            child: Container(
              decoration: const BoxDecoration(
                color: kColorRed,
                borderRadius:
                    const BorderRadius.all(const Radius.circular(20.0)),
              ),
              child: const Text(
                'NEW',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          const Icon(
            Icons.keyboard_arrow_right,
            color: const Color(0xFFB9B9B9),
          ),
        ],
      ),
    );
  }
}
