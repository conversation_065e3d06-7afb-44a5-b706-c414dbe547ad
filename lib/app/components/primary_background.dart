import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/ok_colors.dart';

class PrimaryBackground extends StatelessWidget {
  final Widget child;

  PrimaryBackground({
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Background(
      background: ColoredBox(
        color: OkColors.primary,
        child: SvgPicture.asset(
          'assets/images/primary_background.svg',
          width: double.infinity,
          alignment: Alignment.topCenter,
          fit: BoxFit.fitWidth,
        ),
      ),
      child: child,
    );
  }
}
