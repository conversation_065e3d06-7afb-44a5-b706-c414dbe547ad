// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars
// ignore: avoid_classes_with_only_static_members
class AppTranslation {
  static Map<String, Map<String, String>> translations = {
    'zh': Locales.zh,
    'en': Locales.en,
  };
}

class LocaleKeys {
  LocaleKeys._();
  static const app_id = 'app_id';
  static const app_name = 'app_name';
  static const client_code = 'client_code';
  static const hint_client_code = 'hint_client_code';
  static const channel_code = 'channel_code';
  static const hint_channel_code = 'hint_channel_code';
  static const username = 'username';
  static const hint_username = 'hint_username';
  static const password = 'password';
  static const hint_password = 'hint_password';
  static const remember_me = 'remember_me';
  static const login = 'login';
  static const sign_up_for_free = 'sign_up_for_free';
  static const sign_up = 'sign_up';
  static const build = 'build';
  static const welcom = 'welcom';
  static const version = 'version';
  static const msg_must_to_have = 'msg_must_to_have';
  static const okay = 'okay';
  static const msg_code_0100 = 'msg_code_0100';
  static const msg_code_0101 = 'msg_code_0101';
  static const msg_code_0102 = 'msg_code_0102';
  static const msg_code_0103 = 'msg_code_0103';
  static const msg_code_1101 = 'msg_code_1101';
  static const msg_code_1102 = 'msg_code_1102';
  static const msg_code_1103 = 'msg_code_1103';
  static const msg_code_1104 = 'msg_code_1104';
  static const msg_code_1105 = 'msg_code_1105';
  static const msg_code_1201 = 'msg_code_1201';
  static const msg_code_1202 = 'msg_code_1202';
  static const msg_code_2100 = 'msg_code_2100';
  static const msg_code_2200 = 'msg_code_2200';
  static const msg_code_2300 = 'msg_code_2300';
  static const msg_code_2301 = 'msg_code_2301';
  static const msg_code_2302 = 'msg_code_2302';
  static const msg_code_3100 = 'msg_code_3100';
  static const msg_code_3101 = 'msg_code_3101';
  static const msg_code_3200 = 'msg_code_3200';
  static const msg_code_3300 = 'msg_code_3300';
  static const msg_code_3400 = 'msg_code_3400';
  static const msg_code_3401 = 'msg_code_3401';
  static const msg_code_5300 = 'msg_code_5300';
  static const msg_code_6300 = 'msg_code_6300';
  static const msg_code_6301 = 'msg_code_6301';
  static const msg_code_7100 = 'msg_code_7100';
  static const msg_code_7200 = 'msg_code_7200';
  static const msg_code_7201 = 'msg_code_7201';
  static const msg_code_10000 = 'msg_code_10000';
  static const msg_code_11000 = 'msg_code_11000';
  static const my_fans = 'my_fans';
  static const hint_mobile_nickname_email = 'hint_mobile_nickname_email';
  static const msg_fetch_points_failed = 'msg_fetch_points_failed';
  static const member_points = 'member_points';
  static const expiry_points = 'expiry_points';
  static const order_completed = 'order_completed';
  static const refund = 'refund';
  static const order_history = 'order_history';
  static const order_status_0 = 'order_status_0';
  static const order_status_1 = 'order_status_1';
  static const order_status_2 = 'order_status_2';
  static const order_status_3 = 'order_status_3';
  static const order_status_4 = 'order_status_4';
  static const order_status_5 = 'order_status_5';
  static const status_unknown = 'status_unknown';
  static const store_account_name = 'store_account_name';
  static const order_number = 'order_number';
  static const order_payment_status_0 = 'order_payment_status_0';
  static const order_payment_status_1 = 'order_payment_status_1';
  static const order_payment_status_2 = 'order_payment_status_2';
  static const order_payment_status_3 = 'order_payment_status_3';
  static const order_payment_status_4 = 'order_payment_status_4';
  static const consume_on_site = 'consume_on_site';
  static const consume_at = 'consume_at';
  static const today = 'today';
  static const all = 'all';
  static const order_status = 'order_status';
}

class Locales {
  static const zh = {
    'app_id': 'tw.omos.guests',
    'app_name': '集客多',
    'client_code': '集團代號',
    'hint_client_code': '請輸入集團代號',
    'channel_code': '通路代號',
    'hint_channel_code': '請輸入通路代號',
    'username': '員工帳號',
    'hint_username': '請輸入員工帳號',
    'password': '員工密碼',
    'hint_password': '請輸入員工密碼',
    'remember_me': '記住集團代號及通路代號',
    'login': '登入',
    'sign_up_for_free': '申請免費體驗帳號',
    'sign_up': '我要註冊',
    'build': '建置',
    'welcom': '歡迎',
    'version': '版本',
    'msg_must_to_have': '必填項目',
    'okay': '確定',
    'msg_code_0100': 'token非法 or 過期',
    'msg_code_0101': '輸入參數驗證失敗',
    'msg_code_0102': '參數無效',
    'msg_code_0103': '過多的 request 連線',
    'msg_code_1101': 'input 資料為空',
    'msg_code_1102': 'input 資料長度過長',
    'msg_code_1103': 'input 資料格式錯誤',
    'msg_code_1104': '找不到資料',
    'msg_code_1105': '頁數錯誤，找不到資料',
    'msg_code_1201': '圖片使用量不足',
    'msg_code_1202': '檔案上傳數超過最大值',
    'msg_code_2100': '集團代碼重複',
    'msg_code_2200': '品牌代碼重複',
    'msg_code_2300': '通路代碼重複',
    'msg_code_2301': '帳號重複',
    'msg_code_2302': '通路到期與關閉',
    'msg_code_3100': '會員不存在',
    'msg_code_3101': '原密碼驗證錯誤',
    'msg_code_3200': '點數不足',
    'msg_code_3300': '優惠卷失效（過期 or 已使用 or 無效）',
    'msg_code_3400': '問卷已被兌換',
    'msg_code_3401': '問卷兌換已失效(過期 or 問卷被刪除）',
    'msg_code_5300': '優惠卷已被兌換完畢',
    'msg_code_6300': '退貨失敗，優惠卷已使用',
    'msg_code_6301': '退貨失敗，點數不足',
    'msg_code_7100': '優惠卷已存在',
    'msg_code_7200': '被邀請者不可是自己',
    'msg_code_7201': '跨界邀請不存在',
    'msg_code_10000': '問卷一次僅能開啟一個',
    'msg_code_11000': '優惠卷尚未使用完畢，無法刪除',
    'my_fans': '我的會員',
    'hint_mobile_nickname_email': '請輸入手機末3碼、暱稱或信箱',
    'msg_fetch_points_failed': '查詢會員點數失敗',
    'member_points': '會員積點：',
    'expiry_points': '即將到期點數：',
    'order_completed': '訂單完成',
    'refund': '退款',
    'order_history': '消費記錄',
    'order_status_0': '處理中',
    'order_status_1': '已確認',
    'order_status_2': '訂單完成',
    'order_status_3': '訂單取消',
    'order_status_4': '訂單異常',
    'order_status_5': '訂單退貨、退款',
    'status_unknown': '未知狀態',
    'store_account_name': '店員：',
    'order_number': '記錄編號：',
    'order_payment_status_0': '未付款',
    'order_payment_status_1': '未結清',
    'order_payment_status_2': '已付款',
    'order_payment_status_3': '付款失敗',
    'order_payment_status_4': '超過付款時間',
    'consume_on_site': '現場消費',
    'consume_at': '消費時間：',
    'today': '今日',
    'all': '全部',
    'order_status': '訂單狀態',
  };
  static const en = {
    'app_id': 'tw.omos.guests',
    'app_name': '集客多',
    'client_code': '集團代號',
    'hint_client_code': '請輸入集團代號',
    'channel_code': '通路代號',
    'hint_channel_code': '請輸入通路代號',
    'username': '員工帳號',
    'hint_username': '請輸入員工帳號',
    'password': '員工密碼',
    'hint_password': '請輸入員工密碼',
    'remember_me': '記住集團代號及通路代號',
    'login': '登入',
    'sign_up_for_free': '申請免費體驗帳號',
    'sign_up': '我要註冊',
    'build': '建置',
    'welcom': '歡迎',
    'version': '版本',
    'msg_must_to_have': '必填項目',
    'okay': '確定',
    'msg_code_0100': 'token非法 or 過期',
    'msg_code_0101': '輸入參數驗證失敗',
    'msg_code_0102': '參數無效',
    'msg_code_0103': '過多的 request 連線',
    'msg_code_1101': 'input 資料為空',
    'msg_code_1102': 'input 資料長度過長',
    'msg_code_1103': 'input 資料格式錯誤',
    'msg_code_1104': '找不到資料',
    'msg_code_1105': '頁數錯誤，找不到資料',
    'msg_code_1201': '圖片使用量不足',
    'msg_code_1202': '檔案上傳數超過最大值',
    'msg_code_2100': '集團代碼重複',
    'msg_code_2200': '品牌代碼重複',
    'msg_code_2300': '通路代碼重複',
    'msg_code_2301': '帳號重複',
    'msg_code_2302': '通路到期與關閉',
    'msg_code_3100': '會員不存在',
    'msg_code_3101': '原密碼驗證錯誤',
    'msg_code_3200': '點數不足',
    'msg_code_3300': '優惠卷失效（過期 or 已使用 or 無效）',
    'msg_code_3400': '問卷已被兌換',
    'msg_code_3401': '問卷兌換已失效(過期 or 問卷被刪除）',
    'msg_code_5300': '優惠卷已被兌換完畢',
    'msg_code_6300': '退貨失敗，優惠卷已使用',
    'msg_code_6301': '退貨失敗，點數不足',
    'msg_code_7100': '優惠卷已存在',
    'msg_code_7200': '被邀請者不可是自己',
    'msg_code_7201': '跨界邀請不存在',
    'msg_code_10000': '問卷一次僅能開啟一個',
    'msg_code_11000': '優惠卷尚未使用完畢，無法刪除',
    'my_fans': '我的會員',
    'hint_mobile_nickname_email': '請輸入手機末3碼、暱稱或信箱',
    'msg_fetch_points_failed': '查詢會員點數失敗',
    'member_points': '會員積點：',
    'expiry_points': '即將到期點數：',
    'order_completed': '訂單完成',
    'refund': '退款',
    'order_history': '消費記錄',
    'order_status_0': '處理中',
    'order_status_1': '已確認',
    'order_status_2': '訂單完成',
    'order_status_3': '訂單取消',
    'order_status_4': '訂單異常',
    'order_status_5': '訂單退貨、退款',
    'status_unknown': '未知狀態',
    'store_account_name': '店員：',
    'order_number': '記錄編號：',
    'order_payment_status_0': '未付款',
    'order_payment_status_1': '未結清',
    'order_payment_status_2': '已付款',
    'order_payment_status_3': '付款失敗',
    'order_payment_status_4': '超過付款時間',
    'consume_on_site': '現場消費',
    'consume_at': '消費時間：',
    'today': '今日',
    'all': '全部',
    'order_status': '訂單狀態',
  };
}
