import time
import subprocess
import os

COMMAND_CLEAN = "flutter clean"
COMMAND_ANDROID = "bundle exec fastlane deploy_android"
command_android_development = "flutter build apk --flavor development"
command_android_production = "flutter build apk --flavor production"


def execute_command(command):
    print("======= start build =======")
    start = time.time()
    command_run = subprocess.Popen(command, shell=True)
    command_run.wait()
    end = time.time()
    result_code = command_run.returncode
    if result_code != 0:
        print("======= build failed, take: %.2f sec =======" % (end - start))
    else:
        print("======= build success, take: %.2f sec =======" % (end - start))
    return result_code


def main():
    # clean
    ret = execute_command(COMMAND_CLEAN)
    if ret != 0:
        return ret
    # deploy android development
    # ret = execute_command(command_android_development)
    # if ret != 0:
    #     return ret
    # deploy android development
    # ret = execute_command(command_android_production)
    # if ret != 0:
    #     return ret
    # fastlane
    os.chdir("./android")
    print(os.getcwd())
    ret = execute_command(COMMAND_ANDROID)
    if ret != 0:
        return ret
    os.chdir("..")
    print(os.getcwd())


if __name__ == "__main__":
    main()
